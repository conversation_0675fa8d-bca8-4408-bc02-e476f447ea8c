import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserBills } from "@/services/bill";

export async function POST(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const { page = 1, limit = 50 } = await req.json();

    const bills = await getUserBills(user_uuid, page, limit);

    return respData(bills || []);
  } catch (e) {
    console.log("get user bills failed: ", e);
    return respErr("get user bills failed");
  }
}
