# 支付系统完整指南

本文档是 ShipAny Template One 项目支付系统的完整指南，涵盖 Stripe 和 PayPal 双支付系统的配置、使用和维护。

## 📋 目录

- [系统概述](#系统概述)
- [快速开始](#快速开始)
- [架构设计](#架构设计)
- [配置指南](#配置指南)
- [开发调试](#开发调试)
- [生产部署](#生产部署)
- [故障排除](#故障排除)
- [变更记录](#变更记录)

## 系统概述

### 🎯 功能特性

- ✅ **双支付系统**：支持 Stripe 和 PayPal 两种支付方式
- ✅ **互斥配置**：通过环境变量选择，同时只启用一个支付提供商
- ✅ **统一接口**：抽象支付服务接口，易于扩展和维护
- ✅ **完整流程**：支持订阅和一次性付款
- ✅ **多币种**：支持 USD、CNY 等多种货币
- ✅ **Webhook**：完整的事件处理机制
- ✅ **订单管理**：统一的订单状态跟踪

### 🏗️ 架构设计原则

本支付系统严格遵循以下设计原则：

- **DRY (Don't Repeat Yourself)**：通过抽象接口避免重复代码
- **SRP (Single Responsibility Principle)**：每个类只负责一个职责
- **Clean Code**：清晰的命名和简洁的函数

### 🔧 核心组件

```
src/services/payment/
├── index.ts              # 支付服务入口
├── config.ts             # 支付配置管理
├── factory.ts            # 支付服务工厂
├── stripe-service.ts     # Stripe 支付服务
└── paypal-service.ts     # PayPal 支付服务

src/types/payment.ts      # 支付类型定义
src/app/api/
├── checkout/route.ts     # 统一支付 API
├── stripe-notify/route.ts # Stripe Webhook
├── paypal-notify/route.ts # PayPal Webhook
└── paypal-return/route.ts # PayPal 返回处理
```

## 快速开始

### 1️⃣ 选择支付提供商

在 `.env` 文件中设置：

```bash
# 选择支付提供商：'stripe' 或 'paypal'
PAYMENT_PROVIDER=stripe  # 或 paypal
```

### 2️⃣ 配置 Stripe（推荐）

```bash
# Stripe 配置
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_PRIVATE_KEY=sk_test_your_stripe_private_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```

**获取 Stripe 密钥：**
1. 访问 [Stripe Dashboard](https://dashboard.stripe.com/)
2. 进入 "开发者" → "API密钥"
3. 复制可发布密钥和秘密密钥
4. 设置 Webhook 端点：`https://yourdomain.com/api/stripe-notify`

### 3️⃣ 配置 PayPal（可选）

```bash
# PayPal 配置
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_ENVIRONMENT=sandbox  # sandbox 或 live
```

**获取 PayPal 凭据：**
1. 访问 [PayPal Developer](https://developer.paypal.com/)
2. 创建应用程序
3. 获取 Client ID 和 Client Secret
4. 设置 Webhook 端点：`https://yourdomain.com/api/paypal-notify`

成功输出示例：
```
🚀 Payment Configuration Test
📋 Selected payment provider: stripe
🔍 Testing Stripe configuration...
✅ Stripe configuration complete
📊 Test Summary:
✅ Configuration is valid and ready to use
```

## 配置指南

### 环境变量完整配置

```bash
# ============================================================================
# 支付系统配置
# ============================================================================

# 支付提供商选择（必需）
PAYMENT_PROVIDER=stripe  # 或 paypal

# ----------------------------------------------------------------------------
# Stripe 配置（当 PAYMENT_PROVIDER=stripe 时必需）
# ----------------------------------------------------------------------------
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_PRIVATE_KEY=sk_test_your_stripe_private_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# ----------------------------------------------------------------------------
# PayPal 配置（当 PAYMENT_PROVIDER=paypal 时必需）
# ----------------------------------------------------------------------------
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_ENVIRONMENT=sandbox  # sandbox 或 live

# PayPal Webhook 配置（可选）
PAYPAL_WEBHOOK_ID=your_webhook_id
PAYPAL_WEBHOOK_SECRET=your_webhook_secret

# ----------------------------------------------------------------------------
# 支付页面 URL（两种支付方式共用）
# ----------------------------------------------------------------------------
NEXT_PUBLIC_PAY_SUCCESS_URL=http://localhost:3000/my-orders
NEXT_PUBLIC_PAY_FAIL_URL=http://localhost:3000/#pricing
NEXT_PUBLIC_PAY_CANCEL_URL=http://localhost:3000/#pricing
```

### 支付方式对比

| 功能 | Stripe | PayPal |
|------|--------|--------|
| 一次性支付 | ✅ | ✅ |
| 订阅支付 | ✅ | ❌ (待实现) |
| 多币种支持 | ✅ | ✅ |
| 中国支付方式 | ✅ (微信、支付宝) | ❌ |
| Webhook 事件 | ✅ | ✅ |
| 配置复杂度 | 中等 | 简单 |
| 手续费 | 2.9% + $0.30 | 2.9% + $0.30 |

## 开发调试

### 本地开发设置

1. **使用测试环境**
   ```bash
   # Stripe 测试
   STRIPE_PUBLIC_KEY=pk_test_...
   STRIPE_PRIVATE_KEY=sk_test_...
   
   # PayPal 测试
   PAYPAL_ENVIRONMENT=sandbox
   ```

2. **测试支付**
   - **Stripe 测试卡号**：`4242424242424242`
   - **PayPal 测试**：使用沙盒账户

3. **Webhook 测试**
   ```bash
   # 使用 ngrok 暴露本地服务
   npm install -g ngrok
   ngrok http 3000
   
   # 使用生成的 URL 配置 webhook
   # Stripe: https://your-ngrok-url.ngrok.io/api/stripe-notify
   # PayPal: https://your-ngrok-url.ngrok.io/api/paypal-notify
   ```

### 调试工具

1. **API 测试**
   ```javascript
   // 测试支付创建
   const response = await fetch('/api/checkout', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({
       product_id: 'test_plan',
       amount: 999,
       currency: 'usd',
       interval: 'month'
     })
   });
   ```

2. **日志监控**
   ```bash
   # 启用详细日志
   NODE_ENV=development npm run dev
   ```

## 生产部署

### 1. 环境变量更新

```bash
# 使用生产环境密钥
PAYMENT_PROVIDER=stripe  # 或 paypal

# Stripe 生产环境
STRIPE_PUBLIC_KEY=pk_live_your_live_key
STRIPE_PRIVATE_KEY=sk_live_your_live_key

# PayPal 生产环境
PAYPAL_ENVIRONMENT=live
```

### 2. Webhook 配置

确保 webhook 端点可以从外部访问：
- Stripe: `https://yourdomain.com/api/stripe-notify`
- PayPal: `https://yourdomain.com/api/paypal-notify`

### 3. 数据库迁移

```bash
# 如果从旧版本升级，运行数据库迁移
npm run db:migrate
```

### 4. 安全检查

- ✅ 所有敏感配置存储在环境变量中
- ✅ Webhook 签名验证已启用
- ✅ HTTPS 已配置
- ✅ 支付金额服务端验证

## 故障排除

### 常见问题

1. **配置验证失败**
   ```bash
   # 检查环境变量
   echo $PAYMENT_PROVIDER
   echo $STRIPE_PUBLIC_KEY  # 或 $PAYPAL_CLIENT_ID
   ```

2. **支付创建失败**
   - 检查 API 密钥是否有效
   - 验证支付金额和货币格式
   - 查看服务器日志中的详细错误

3. **Webhook 不工作**
   - 确认 webhook URL 可以从外部访问
   - 检查 webhook 签名验证
   - 查看支付服务商仪表板的 webhook 日志

4. **PayPal 登录问题**
   - 沙盒环境需要频繁登录是正常的
   - 生产环境用户登录状态保持更久
   - 无法完全跳过登录，这是安全要求

### 调试技巧

1. **查看当前配置**
   ```javascript
   import { getCurrentPaymentProvider } from '@/services/payment';
   console.log('Current provider:', getCurrentPaymentProvider());
   ```

2. **检查订单状态**
   - 查看数据库中的 `orders` 表
   - 检查 `payment_provider` 和 `status` 字段
   - 查看 `paid_detail` 中的详细信息

## 变更记录

### v2.7.0 - PayPal 支付集成 (2025-07-12)

#### 🆕 新增功能
- **双支付系统**：新增 PayPal 支付支持，与 Stripe 互斥使用
- **统一支付接口**：实现 `IPaymentService` 抽象接口
- **支付服务工厂**：使用工厂模式创建支付服务实例
- **配置管理**：`PaymentConfig` 单例管理支付配置

#### 📁 新增文件 (13个)
1. `src/types/payment.ts` - 支付类型定义
2. `src/services/payment/config.ts` - 支付配置管理
3. `src/services/payment/stripe-service.ts` - Stripe 支付服务
4. `src/services/payment/paypal-service.ts` - PayPal 支付服务
5. `src/services/payment/factory.ts` - 支付服务工厂
6. `src/services/payment/index.ts` - 支付服务入口
7. `src/app/api/paypal-notify/route.ts` - PayPal Webhook 处理
8. `src/app/api/paypal-return/route.ts` - PayPal 返回处理
9. `scripts/test-payment-config.js` - 配置测试脚本
10. `docs/PAYPAL_INTEGRATION.md` - PayPal 集成文档
11. `docs/PAYPAL_WEBHOOK_SETUP.md` - PayPal 配置指南
12. `docs/PAYMENT_SETUP_GUIDE.md` - 支付设置指南
13. `docs/PAYMENT_SYSTEM.md` - 支付系统完整指南（本文档）

#### 🔄 修改文件 (8个)
1. `src/db/schema.ts` - 添加 `payment_provider` 和 `payment_session_id` 字段
2. `src/models/order.ts` - 新增 `updateOrderPaymentSession` 方法
3. `src/services/order.ts` - 新增 `handlePaymentSession` 通用处理方法
4. `src/app/api/checkout/route.ts` - 使用统一支付服务
5. `src/components/blocks/pricing/index.tsx` - 支持多支付方式跳转
6. `src/app/[locale]/pay-success/[session_id]/page.tsx` - 支持 PayPal 返回处理
7. `.env.example` - 添加 PayPal 配置示例
8. `package.json` - 添加配置测试脚本

#### 🏗️ 架构改进
- **设计原则**：严格遵循 DRY、SRP、Clean Code 原则
- **工厂模式**：`PaymentServiceFactory` 统一创建支付服务
- **单例模式**：`PaymentConfig` 管理配置
- **策略模式**：不同支付服务的具体实现
- **向后兼容**：保留原有 Stripe 功能完全不变

#### 🧪 测试验证
- ✅ Stripe 支付完全正常
- ✅ PayPal 支付完全正常
- ✅ 配置切换功能正常
- ✅ 订单管理统一处理
- ✅ Webhook 事件处理正常

---

*更多信息请参考项目根目录的 `README.md` 文件。*
