{"metadata": {"title": "水印去除器 - AI智能去水印工具", "description": "Watermark Remover - 专业的AI智能去水印工具，使用先进的人工智能技术，瞬间去除图片水印。", "keywords": "Watermark Remover, AI去水印, 智能去水印, AI图像处理"}, "user": {"sign_in": "登录", "sign_out": "退出登录", "credits": "额度", "api_keys": "API 密钥", "my_orders": "我的订单", "user_center": "用户中心", "admin_system": "管理后台"}, "user_center": {"remaining_credits": "剩余积分", "credit_details": "积分消费明细", "bill_records": "账单记录", "account_info": "账户信息", "transaction_time": "交易时间", "transaction_type": "交易类型", "credit_change": "积分变化", "transaction_id": "交易号", "loading": "加载中...", "no_records": "暂无积分消费记录", "no_bills": "暂无账单记录", "trans_types": {"new_user": "新用户奖励", "order_pay": "订阅充值", "system_add": "系统赠送", "monthly_gift": "月度赠送", "ping": "测试消费", "remove_watermark": "去水印"}, "bill_purposes": {"credits_purchase": "积分购买", "subscription_monthly": "月度订阅", "subscription_yearly": "年度订阅", "one_time_purchase": "一次性购买"}, "payment_methods": {"alipay": "支付宝", "wechat": "微信支付", "unknown": "未知"}, "bill_headers": {"transaction_time": "交易时间", "transaction_type": "交易类型", "purpose": "用途", "payment_method": "支付方式", "amount": "金额", "credits": "积分"}, "account": {"nickname": "昵称", "id": "ID", "email": "邮箱", "payment_method": "支付方式", "paypal_enabled": "已启用", "paypal_description": "安全、快速的国际支付方式，支持信用卡和PayPal余额支付"}}, "sign_modal": {"sign_in_title": "登录", "sign_in_description": "登录您的账户", "sign_up_title": "注册", "sign_up_description": "创建新账户", "email_title": "邮箱", "email_placeholder": "请输入您的邮箱", "password_title": "密码", "password_placeholder": "请输入您的密码", "forgot_password": "忘记密码？", "or": "或", "continue": "继续", "no_account": "还没有账户？", "email_sign_in": "使用邮箱登录", "google_sign_in": "使用 Google 登录", "github_sign_in": "使用 GitHub 登录", "close_title": "关闭", "cancel_title": "取消"}, "my_orders": {"title": "我的订单", "description": "在 ShipAny 上购买的订单。", "no_orders": "未找到订单", "tip": "", "activate_order": "激活订单", "actived": "已激活", "join_discord": "加入 Discord", "read_docs": "阅读文档", "table": {"order_no": "订单号", "email": "邮箱", "product_name": "产品名称", "amount": "金额", "paid_at": "支付时间", "github_username": "GitHub 用户名", "status": "状态"}}, "my_credits": {"title": "我的积分", "left_tip": "剩余积分: {left_credits}", "no_credits": "没有积分记录", "recharge": "充值", "table": {"trans_no": "交易号", "trans_type": "交易类型", "credits": "积分", "updated_at": "更新时间", "status": "状态"}}, "api_keys": {"title": "API 密钥", "tip": "请妥善保管您的 API 密钥，避免泄露", "no_api_keys": "没有 API 密钥", "create_api_key": "创建 API 密钥", "table": {"name": "名称", "key": "密钥", "created_at": "创建时间"}, "form": {"name": "名称", "name_placeholder": "API 密钥名称", "submit": "提交"}}, "blog": {"title": "博客", "description": "关于 ShipAny 的新闻、资源和更新", "read_more_text": "阅读更多"}, "my_invites": {"title": "我的邀请", "description": "查看您的邀请记录", "no_invites": "未找到邀请记录", "my_invite_link": "我的邀请链接", "edit_invite_link": "编辑邀请链接", "copy_invite_link": "复制邀请链接", "invite_code": "邀请码", "invite_tip": "每邀请 1 位朋友购买 ShipAny，奖励 $50。", "invite_balance": "邀请奖励余额", "total_invite_count": "总邀请人数", "total_paid_count": "已充值人数", "total_award_amount": "总奖励金额", "update_invite_code": "设置邀请码", "update_invite_code_tip": "输入你的自定义邀请码", "update_invite_button": "保存", "no_orders": "你需要先购买过 ShipAny 才能邀请朋友", "no_affiliates": "你暂无邀请朋友的权限，请联系我们申请开通。", "table": {"invite_time": "邀请时间", "invite_user": "邀请用户", "status": "状态", "reward_percent": "奖励比例", "reward_amount": "奖励金额", "pending": "已注册，未支付", "completed": "已支付"}}, "feedback": {"title": "反馈", "description": "我们很乐意听取您对产品的看法或如何改进产品体验。", "submit": "提交", "loading": "提交中...", "contact_tip": "其他联系方式", "rating_tip": "您对 ShipAny 的看法如何？", "placeholder": "在这里留下您的反馈..."}, "homepage": {"title": "AI智能去水印，精准高效", "description": "使用先进的AI技术瞬间去除图片水印。快速、精准，个人使用完全免费。", "upload_text": "拖拽图片到此处或点击上传", "supported_formats": "支持 JPG、PNG、JPEG、BMP 格式", "remove_watermark": "去除水印", "processing": "正在去除水印...", "download": "下载去水印图片", "features_title": "强大的AI功能", "steps_title": "使用步骤", "testimonials_title": "用户评价", "features": [{"title": "AI智能精准", "description": "先进AI算法，精准去除图片水印，保留原图质量。"}, {"title": "瞬间完成", "description": "云端极速处理，几秒钟即可获得无水印图片。"}, {"title": "100%安全", "description": "图片全程加密处理，处理后自动删除，隐私有保障。"}, {"title": "多种格式", "description": "支持JPG、JPEG、PNG、BMP等主流格式。"}, {"title": "移动友好", "description": "完美适配手机、平板和桌面端，随时随地处理图片。"}, {"title": "无二次水印", "description": "输出图片无任何二次水印或广告，干净纯净。"}], "steps": [{"title": "上传图片", "description": "拖拽或点击上传带水印的图片，支持多种格式。"}, {"title": "AI去水印", "description": "AI自动分析并去除图片水印，保留原图细节。"}, {"title": "下载结果", "description": "一键下载无水印高清图片，立即使用。"}], "testimonials": [{"name": "陈小雅", "role": "摄影师", "content": "这个工具帮我节省了大量修图时间，AI去水印效果非常自然，原图质量也保留得很好。"}, {"name": "王建国", "role": "市场总监", "content": "本来很怀疑AI能不能处理复杂水印，结果超出预期，推荐给所有需要批量处理图片的同事！"}, {"name": "<PERSON>小美", "role": "设计师", "content": "界面简洁，操作流畅，移动端体验也很棒，随时随地都能用。"}], "quick_features": ["快速", "私密", "简单"]}}