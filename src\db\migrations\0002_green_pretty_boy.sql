CREATE TABLE "bills" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "bills_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START WITH 1 CACHE 1),
	"bill_no" varchar(255) NOT NULL,
	"created_at" timestamp with time zone NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"user_email" varchar(255) NOT NULL,
	"transaction_type" varchar(50) NOT NULL,
	"purpose" varchar(100) NOT NULL,
	"order_no" varchar(255),
	"payment_method" varchar(50),
	"amount" integer NOT NULL,
	"currency" varchar(10) DEFAULT 'usd' NOT NULL,
	"credits" integer DEFAULT 0,
	"status" varchar(50) DEFAULT 'completed' NOT NULL,
	"description" text,
	"metadata" text,
	CONSTRAINT "bills_bill_no_unique" UNIQUE("bill_no")
);
--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "is_admin" boolean DEFAULT false NOT NULL;