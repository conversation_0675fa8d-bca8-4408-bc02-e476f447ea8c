export interface Bill {
  id?: number;
  bill_no: string;
  created_at: string | Date;
  user_uuid: string;
  user_email: string;
  transaction_type: string;
  purpose: string;
  order_no?: string;
  payment_method?: string;
  amount: number;
  currency: string;
  credits?: number;
  status: string;
  description?: string;
  metadata?: string;
}

export interface CreateBillParams {
  user_uuid: string;
  user_email: string;
  transaction_type: string;
  purpose: string;
  order_no?: string;
  payment_method?: string;
  amount: number;
  currency: string;
  credits?: number;
  description?: string;
  metadata?: any;
}
