import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { isCurrentUserAdmin } from "@/services/admin";

export async function GET(req: NextRequest) {
  try {
    // 检查用户认证
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return NextResponse.json({ isAdmin: false });
    }

    // 检查是否为管理员
    const isAdmin = await isCurrentUserAdmin();
    
    return NextResponse.json({ isAdmin });
  } catch (error) {
    console.error("Check admin failed:", error);
    return NextResponse.json({ isAdmin: false });
  }
}
