import React from "react";

const DashboardPage = () => {
  // TODO: 后续接入用户信息、积分、历史记录API
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-100 flex flex-col items-center">
      <header className="w-full py-6 flex justify-center items-center border-b bg-white shadow-sm">
        <img src="/logo.png" alt="Watermark Remover" className="h-10 mr-3" />
        <span className="text-2xl font-bold tracking-tight">用户中心</span>
      </header>
      <main className="flex-1 w-full max-w-xl flex flex-col items-center justify-center px-4 py-10">
        <h1 className="text-2xl font-bold mb-6 text-center">欢迎回来！</h1>
        <div className="w-full bg-white rounded-lg shadow p-6 mb-6">
          <div className="mb-2 text-gray-700 font-semibold">用户信息</div>
          <div className="text-sm text-gray-500 mb-2">邮箱：<EMAIL></div>
          <div className="text-sm text-gray-500 mb-2">积分/额度：<span className="font-bold text-blue-600">3</span></div>
          <div className="text-sm text-gray-500">订阅状态：<span className="font-bold text-green-600">免费用户</span></div>
        </div>
        <a href="/history" className="w-full py-2 px-4 bg-blue-600 text-white rounded font-semibold text-center hover:bg-blue-700 transition mb-4">查看历史处理记录</a>
        <a href="/pricing" className="w-full py-2 px-4 bg-green-600 text-white rounded font-semibold text-center hover:bg-green-700 transition">升级订阅/购买积分</a>
      </main>
      <footer className="w-full py-6 text-center text-gray-400 text-xs border-t bg-white mt-10">
        &copy; {new Date().getFullYear()} Watermark Remover. Powered by Next.js.
      </footer>
    </div>
  );
};

export default DashboardPage;
