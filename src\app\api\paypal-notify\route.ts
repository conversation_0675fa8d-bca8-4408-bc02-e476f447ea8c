import { respOk } from "@/lib/resp";
import { handlePaymentSession } from "@/services/order";
import { getPaymentService } from "@/services/payment";

/**
 * PayPal Webhook处理
 */
export async function POST(req: Request) {
  try {
    const body = await req.text();
    const signature = req.headers.get("paypal-transmission-signature") || "";
    
    const paymentService = getPaymentService();
    
    // 验证webhook签名
    const event = paymentService.verifyWebhookSignature(
      body,
      signature,
      process.env.PAYPAL_WEBHOOK_SECRET || ""
    );

    console.log("PayPal notify event: ", event);

    // 处理不同的事件类型
    switch (event.event_type) {
      case "PAYMENT.CAPTURE.COMPLETED": {
        // 一次性支付完成
        const resource = event.resource;
        console.log("PayPal payment capture completed:", resource);

        if (resource && (resource.custom_id || resource.invoice_id)) {
          try {
            // 使用capture ID获取支付会话信息
            const orderId = resource.supplementary_data?.related_ids?.order_id || resource.id;
            const session = await paymentService.getPaymentSession(orderId);
            await handlePaymentSession(session);
            console.log("PayPal payment processed successfully for order:", session.metadata?.order_no);
          } catch (error) {
            console.error("Failed to process PayPal payment:", error);
            throw error;
          }
        }
        break;
      }

      case "CHECKOUT.ORDER.APPROVED": {
        // 订单已批准，但还未捕获
        console.log("PayPal order approved, will be captured on return URL");
        break;
      }

      case "BILLING.SUBSCRIPTION.ACTIVATED": {
        // 订阅激活
        const resource = event.resource;
        console.log("PayPal subscription activated:", resource);

        if (resource && resource.custom_id) {
          try {
            const session = await paymentService.getPaymentSession(resource.id);
            await handlePaymentSession(session);
            console.log("PayPal subscription processed successfully for order:", session.metadata?.order_no);
          } catch (error) {
            console.error("Failed to process PayPal subscription:", error);
            throw error;
          }
        }
        break;
      }

      default:
        console.log("PayPal event not handled: ", event.event_type);
    }

    return respOk();
  } catch (e: any) {
    console.log("PayPal notify failed: ", e);
    return Response.json(
      { error: `Handle PayPal notify failed: ${e.message}` },
      { status: 500 }
    );
  }
}
