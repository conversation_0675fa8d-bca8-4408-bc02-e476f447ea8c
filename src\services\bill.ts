import {
  insertBill,
  findBillByOrderNo,
  getBillsByUserUuid,
  BillTransactionType,
  BillPurpose,
  BillStatus,
} from "@/models/bill";
import { bills as billsTable } from "@/db/schema";
import { getSnowId } from "@/lib/hash";
import { getIsoTimestr } from "@/lib/time";
import { CreateBillParams } from "@/types/bill";
import { Order } from "@/types/order";

/**
 * 创建账单记录
 */
export async function createBill(params: CreateBillParams) {
  try {
    const bill_no = getSnowId();
    const now = new Date(getIsoTimestr());

    const billData: typeof billsTable.$inferInsert = {
      bill_no,
      created_at: now,
      user_uuid: params.user_uuid,
      user_email: params.user_email,
      transaction_type: params.transaction_type,
      purpose: params.purpose,
      order_no: params.order_no,
      payment_method: params.payment_method,
      amount: params.amount,
      currency: params.currency,
      credits: params.credits || 0,
      status: BillStatus.Completed,
      description: params.description,
      metadata: params.metadata ? JSON.stringify(params.metadata) : null,
    };

    const bill = await insertBill(billData);
    return bill;
  } catch (error) {
    console.error("Create bill error:", error);
    throw new Error("Failed to create bill");
  }
}

/**
 * 从订单创建账单记录
 */
export async function createBillFromOrder(order: Order) {
  try {
    // 检查是否已经存在账单记录
    const existingBill = await findBillByOrderNo(order.order_no);
    if (existingBill) {
      console.log(`Bill already exists for order ${order.order_no}`);
      return existingBill;
    }

    // 确定交易类型和用途
    let purpose = BillPurpose.OneTimePurchase;
    if (order.interval === 'monthly') {
      purpose = BillPurpose.SubscriptionMonthly;
    } else if (order.interval === 'yearly') {
      purpose = BillPurpose.SubscriptionYearly;
    } else if (order.credits > 0) {
      purpose = BillPurpose.CreditsPurchase;
    }

    const billParams: CreateBillParams = {
      user_uuid: order.user_uuid,
      user_email: order.user_email,
      transaction_type: BillTransactionType.Payment,
      purpose,
      order_no: order.order_no,
      payment_method: order.payment_provider || 'unknown',
      amount: order.amount,
      currency: order.currency,
      credits: order.credits,
      description: order.product_name || `订单 ${order.order_no}`,
      metadata: {
        interval: order.interval,
        valid_months: order.valid_months,
        product_id: order.product_id,
        expired_at: order.expired_at,
      },
    };

    const bill = await createBill(billParams);
    console.log(`Bill created for order ${order.order_no}: ${bill?.bill_no}`);
    return bill;
  } catch (error) {
    console.error("Create bill from order error:", error);
    throw new Error("Failed to create bill from order");
  }
}

/**
 * 获取用户账单记录
 */
export async function getUserBills(
  user_uuid: string,
  page: number = 1,
  limit: number = 50
) {
  try {
    const bills = await getBillsByUserUuid(user_uuid, page, limit);
    return bills || [];
  } catch (error) {
    console.error("Get user bills error:", error);
    throw new Error("Failed to get user bills");
  }
}

/**
 * 获取账单显示名称
 */
export function getBillPurposeDisplay(purpose: string): string {
  switch (purpose) {
    case BillPurpose.CreditsPurchase:
      return "积分购买";
    case BillPurpose.SubscriptionMonthly:
      return "月度订阅";
    case BillPurpose.SubscriptionYearly:
      return "年度订阅";
    case BillPurpose.OneTimePurchase:
      return "一次性购买";
    default:
      return purpose;
  }
}

/**
 * 获取交易类型显示名称
 */
export function getBillTransactionTypeDisplay(transactionType: string): string {
  switch (transactionType) {
    case BillTransactionType.Payment:
      return "支付";
    case BillTransactionType.Refund:
      return "退款";
    case BillTransactionType.Subscription:
      return "订阅";
    default:
      return transactionType;
  }
}

/**
 * 获取支付方式显示名称
 */
export function getPaymentMethodDisplay(paymentMethod: string): string {
  switch (paymentMethod?.toLowerCase()) {
    case 'paypal':
      return 'PayPal';
    case 'stripe':
      return 'Stripe';
    case 'alipay':
      return '支付宝';
    case 'wechat':
      return '微信支付';
    default:
      return paymentMethod || '未知';
  }
}
