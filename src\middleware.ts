import createMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";
import { NextRequest, NextResponse } from "next/server";

const intlMiddleware = createMiddleware(routing);

export default function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 重定向旧的法律页面路由到新的多语言路由
  if (pathname === '/privacy-policy') {
    const locale = request.cookies.get('NEXT_LOCALE')?.value || 'en';
    return NextResponse.redirect(new URL(`/${locale}/privacy-policy`, request.url));
  }

  if (pathname === '/terms-of-service') {
    const locale = request.cookies.get('NEXT_LOCALE')?.value || 'en';
    return NextResponse.redirect(new URL(`/${locale}/terms-of-service`, request.url));
  }

  return intlMiddleware(request);
}

export const config = {
  matcher: [
    "/",
    "/(en|en-US|zh|zh-CN|zh-TW|zh-HK|zh-MO|fr|ru|pt|ja|ko|de|ar|es|it)/:path*",
    "/((?!api/|_next|_vercel|.*\\..*).*)",
  ],
};
