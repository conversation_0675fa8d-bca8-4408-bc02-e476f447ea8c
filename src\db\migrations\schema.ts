import { pgTable, integer, varchar, timestamp, unique, text, uniqueIndex, boolean } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const affiliates = pgTable("affiliates", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "affiliates_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	userUuid: varchar("user_uuid", { length: 255 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	status: varchar({ length: 50 }).default('active').notNull(),
	invitedBy: varchar("invited_by", { length: 255 }).notNull(),
	paidOrderNo: varchar("paid_order_no", { length: 255 }).default('').notNull(),
	paidAmount: integer("paid_amount").default(0).notNull(),
	rewardPercent: integer("reward_percent").default(0).notNull(),
	rewardAmount: integer("reward_amount").default(0).notNull(),
});

export const apikeys = pgTable("apikeys", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "apikeys_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	apiKey: varchar("api_key", { length: 255 }).notNull(),
	title: varchar({ length: 100 }),
	userUuid: varchar("user_uuid", { length: 255 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	status: varchar({ length: 50 }),
}, (table) => [
	unique("apikeys_api_key_unique").on(table.apiKey),
]);

export const credits = pgTable("credits", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "credits_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	transNo: varchar("trans_no", { length: 255 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	userUuid: varchar("user_uuid", { length: 255 }).notNull(),
	transType: varchar("trans_type", { length: 50 }).notNull(),
	credits: integer().notNull(),
	orderNo: varchar("order_no", { length: 255 }),
	expiredAt: timestamp("expired_at", { withTimezone: true, mode: 'string' }),
}, (table) => [
	unique("credits_trans_no_unique").on(table.transNo),
]);

export const feedbacks = pgTable("feedbacks", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "feedbacks_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	status: varchar({ length: 50 }),
	userUuid: varchar("user_uuid", { length: 255 }),
	content: text(),
	rating: integer(),
});

export const posts = pgTable("posts", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "posts_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	uuid: varchar({ length: 255 }).notNull(),
	slug: varchar({ length: 255 }),
	title: varchar({ length: 255 }),
	description: text(),
	content: text(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	status: varchar({ length: 50 }),
	coverUrl: varchar("cover_url", { length: 255 }),
	authorName: varchar("author_name", { length: 255 }),
	authorAvatarUrl: varchar("author_avatar_url", { length: 255 }),
	locale: varchar({ length: 50 }),
}, (table) => [
	unique("posts_uuid_unique").on(table.uuid),
]);

export const orders = pgTable("orders", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "orders_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	orderNo: varchar("order_no", { length: 255 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	userUuid: varchar("user_uuid", { length: 255 }).default('').notNull(),
	userEmail: varchar("user_email", { length: 255 }).default('').notNull(),
	amount: integer().notNull(),
	interval: varchar({ length: 50 }),
	expiredAt: timestamp("expired_at", { withTimezone: true, mode: 'string' }),
	status: varchar({ length: 50 }).notNull(),
	stripeSessionId: varchar("stripe_session_id", { length: 255 }),
	credits: integer().notNull(),
	currency: varchar({ length: 50 }),
	subId: varchar("sub_id", { length: 255 }),
	subIntervalCount: integer("sub_interval_count"),
	subCycleAnchor: integer("sub_cycle_anchor"),
	subPeriodEnd: integer("sub_period_end"),
	subPeriodStart: integer("sub_period_start"),
	subTimes: integer("sub_times"),
	productId: varchar("product_id", { length: 255 }),
	productName: varchar("product_name", { length: 255 }),
	validMonths: integer("valid_months"),
	orderDetail: text("order_detail"),
	paidAt: timestamp("paid_at", { withTimezone: true, mode: 'string' }),
	paidEmail: varchar("paid_email", { length: 255 }),
	paidDetail: text("paid_detail"),
	paymentProvider: varchar("payment_provider", { length: 50 }).default('stripe'),
	paymentSessionId: varchar("payment_session_id", { length: 255 }),
}, (table) => [
	unique("orders_order_no_unique").on(table.orderNo),
]);

export const users = pgTable("users", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "users_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	uuid: varchar({ length: 255 }).notNull(),
	email: varchar({ length: 255 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }),
	nickname: varchar({ length: 255 }),
	avatarUrl: varchar("avatar_url", { length: 255 }),
	locale: varchar({ length: 50 }),
	signinType: varchar("signin_type", { length: 50 }),
	signinIp: varchar("signin_ip", { length: 255 }),
	signinProvider: varchar("signin_provider", { length: 50 }),
	signinOpenid: varchar("signin_openid", { length: 255 }),
	inviteCode: varchar("invite_code", { length: 255 }).default('').notNull(),
	updatedAt: timestamp("updated_at", { withTimezone: true, mode: 'string' }),
	invitedBy: varchar("invited_by", { length: 255 }).default('').notNull(),
	isAffiliate: boolean("is_affiliate").default(false).notNull(),
	isAdmin: boolean("is_admin").default(false).notNull(),
}, (table) => [
	uniqueIndex("email_provider_unique_idx").using("btree", table.email.asc().nullsLast().op("text_ops"), table.signinProvider.asc().nullsLast().op("text_ops")),
	unique("users_uuid_unique").on(table.uuid),
]);

export const bills = pgTable("bills", {
	id: integer().primaryKey().generatedAlwaysAsIdentity({ name: "bills_id_seq", startWith: 1, increment: 1, minValue: 1, maxValue: **********, cache: 1 }),
	billNo: varchar("bill_no", { length: 255 }).notNull(),
	createdAt: timestamp("created_at", { withTimezone: true, mode: 'string' }).notNull(),
	userUuid: varchar("user_uuid", { length: 255 }).notNull(),
	userEmail: varchar("user_email", { length: 255 }).notNull(),
	transactionType: varchar("transaction_type", { length: 50 }).notNull(),
	purpose: varchar({ length: 100 }).notNull(),
	orderNo: varchar("order_no", { length: 255 }),
	paymentMethod: varchar("payment_method", { length: 50 }),
	amount: integer().notNull(),
	currency: varchar({ length: 10 }).default('usd').notNull(),
	credits: integer().default(0),
	status: varchar({ length: 50 }).default('completed').notNull(),
	description: text(),
	metadata: text(),
}, (table) => [
	unique("bills_bill_no_unique").on(table.billNo),
]);
