'use client';

import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Check, CreditCard, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';

const PaymentSuccessPage = () => {
  const searchParams = useSearchParams();
  const [paymentInfo, setPaymentInfo] = useState({
    provider: 'paypal',
    amount: '',
    credits: '',
    orderNo: '',
  });

  useEffect(() => {
    // 从URL参数获取支付信息
    const provider = searchParams.get('provider') || 'paypal';
    const amount = searchParams.get('amount') || '';
    const credits = searchParams.get('credits') || '';
    const orderNo = searchParams.get('order_no') || '';

    setPaymentInfo({
      provider,
      amount,
      credits,
      orderNo,
    });
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Success Animation */}
        <div className="text-center mb-8">
          <div className="relative mx-auto w-24 h-24 mb-6">
            <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 rounded-full animate-pulse"></div>
            <div className="relative bg-gradient-to-r from-green-500 to-green-700 rounded-full w-24 h-24 flex items-center justify-center">
              <Check className="w-12 h-12 text-white" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">支付成功！</h1>
          <p className="text-gray-300">您的支付已通过 PayPal 成功处理</p>
        </div>

        {/* Payment Details Card */}
        <div className="bg-black/40 backdrop-blur-sm rounded-2xl p-6 border border-white/10 mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-blue-500/20 p-2 rounded-lg">
              <CreditCard className="w-5 h-5 text-blue-400" />
            </div>
            <h2 className="text-lg font-semibold text-white">支付详情</h2>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-gray-400">支付方式</span>
              <div className="flex items-center gap-2">
                <img src="/imgs/logos/paypal.png" alt="PayPal" className="h-5" />
                <span className="text-white font-medium">PayPal</span>
              </div>
            </div>

            {paymentInfo.credits && (
              <div className="flex justify-between items-center">
                <span className="text-gray-400">获得积分</span>
                <span className="text-green-400 font-bold">+{paymentInfo.credits}</span>
              </div>
            )}

            {paymentInfo.amount && (
              <div className="flex justify-between items-center">
                <span className="text-gray-400">支付金额</span>
                <span className="text-white font-medium">${paymentInfo.amount}</span>
              </div>
            )}

            {paymentInfo.orderNo && (
              <div className="flex justify-between items-center">
                <span className="text-gray-400">订单号</span>
                <span className="text-white font-mono text-sm">{paymentInfo.orderNo}</span>
              </div>
            )}

            <div className="flex justify-between items-center">
              <span className="text-gray-400">状态</span>
              <span className="text-green-400 font-medium">已完成</span>
            </div>
          </div>
        </div>

        {/* Success Message */}
        <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-6 border border-green-500/30 mb-6">
          <h3 className="text-white font-semibold mb-2">🎉 积分已添加到您的账户</h3>
          <p className="text-gray-300 text-sm">
            您可以立即开始使用新增的积分来去除图片水印。积分将根据您的订阅计划自动续期。
          </p>
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          <Button asChild className="w-full bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-semibold py-3">
            <Link href="/my-orders" className="flex items-center justify-center gap-2">
              <span>查看订单历史</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </Button>

          <Button asChild variant="outline" className="w-full border-white/20 text-white hover:bg-white/10">
            <Link href="/">
              返回首页
            </Link>
          </Button>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-gray-400 text-sm">
            如有任何问题，请联系我们的客服团队
          </p>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessPage;
