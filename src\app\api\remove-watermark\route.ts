import { NextRequest, NextResponse } from "next/server";
import { getUserUuid } from "@/services/user";
import { decreaseCredits, CreditsTransType, CreditsAmount, getUserCredits } from "@/services/credit";

const TEXTIN_API_URL = "https://api.textin.com/ai/service/v1/image/watermark_remove";
const APP_ID = "f0fee7f6142b26899544bf7a1486cb96";
const SECRET_CODE = "6ebf2a8b1e05bc74589ed39432bb4fe1";

// 移除 edge runtime，因为需要使用 Buffer

export async function POST(req: NextRequest) {
  try {
    // 检查用户认证
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return NextResponse.json({ error: "用户未登录" }, { status: 401 });
    }

    // 检查用户积分
    const userCredits = await getUserCredits(user_uuid);
    if (!userCredits || userCredits.left_credits < CreditsAmount.RemoveWatermarkCost) {
      return NextResponse.json({
        error: "积分不足，请先充值",
        required_credits: CreditsAmount.RemoveWatermarkCost,
        current_credits: userCredits?.left_credits || 0
      }, { status: 402 });
    }

    const formData = await req.formData();
    const file = formData.get("file");
    if (!file || !(file instanceof Blob)) {
      return NextResponse.json({ error: "未上传图片文件" }, { status: 400 });
    }

    // 将图片转为二进制流
    const arrayBuffer = await file.arrayBuffer();

    const res = await fetch(TEXTIN_API_URL, {
      method: "POST",
      headers: {
        "x-ti-app-id": APP_ID,
        "x-ti-secret-code": SECRET_CODE,
        "Content-Type": "application/octet-stream",
      },
      body: Buffer.from(arrayBuffer),
    });

    const data = await res.json();
    if (data && data.code === 200 && data.result && data.result.image) {
      // API调用成功，扣减积分
      try {
        await decreaseCredits({
          user_uuid,
          trans_type: CreditsTransType.RemoveWatermark,
          credits: CreditsAmount.RemoveWatermarkCost,
        });
        console.log("Successfully decreased credits for user:", user_uuid);
      } catch (creditError) {
        console.error("Failed to decrease credits:", creditError);
        return NextResponse.json({ error: "积分扣除失败" }, { status: 500 });
      }

      // 返回base64图片
      return NextResponse.json({ image: data.result.image });
    } else {
      return NextResponse.json({ error: data.message || "去水印失败", detail: data }, { status: 500 });
    }
  } catch (error) {
    console.error("remove watermark failed:", error);
    return NextResponse.json({ error: "服务器错误" }, { status: 500 });
  }
}