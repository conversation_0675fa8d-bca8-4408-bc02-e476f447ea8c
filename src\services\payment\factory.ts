import { IPaymentService, PaymentProvider } from '@/types/payment';
import { PaymentConfig } from './config';
import { StripePaymentService } from './stripe-service';
import { PayPalPaymentService } from './paypal-service';

/**
 * 支付服务工厂
 * 遵循工厂模式和DRY原则：统一创建支付服务实例
 */
export class PaymentServiceFactory {
  private static paymentService: IPaymentService | null = null;

  /**
   * 获取支付服务实例（单例模式）
   */
  public static getPaymentService(): IPaymentService {
    if (!PaymentServiceFactory.paymentService) {
      PaymentServiceFactory.paymentService = PaymentServiceFactory.createPaymentService();
    }
    return PaymentServiceFactory.paymentService;
  }

  /**
   * 创建支付服务实例
   */
  private static createPaymentService(): IPaymentService {
    const config = PaymentConfig.getInstance();
    const provider = config.getProvider();

    switch (provider) {
      case PaymentProvider.STRIPE:
        return new StripePaymentService();
      case PaymentProvider.PAYPAL:
        return new PayPalPaymentService();
      default:
        throw new Error(`Unsupported payment provider: ${provider}`);
    }
  }

  /**
   * 重置支付服务实例（主要用于测试）
   */
  public static resetPaymentService(): void {
    PaymentServiceFactory.paymentService = null;
  }

  /**
   * 获取当前支付提供商
   */
  public static getCurrentProvider(): PaymentProvider {
    const config = PaymentConfig.getInstance();
    return config.getProvider();
  }
}
