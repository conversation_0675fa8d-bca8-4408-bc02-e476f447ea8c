'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { useAppContext } from '@/contexts/app';

export default function PaymentSuccessToast() {
  const searchParams = useSearchParams();
  const { user, setUser } = useAppContext();

  // 刷新用户信息的函数
  const refreshUserInfo = async () => {
    try {
      const resp = await fetch("/api/get-user-info", {
        method: "POST",
      });

      if (!resp.ok) {
        throw new Error("fetch user info failed with status: " + resp.status);
      }

      const { code, message, data } = await resp.json();
      if (code !== 0) {
        throw new Error(message);
      }

      setUser(data);
    } catch (e) {
      console.log("refresh user info failed:", e);
    }
  };

  useEffect(() => {
    const payment = searchParams.get('payment');
    const credits = searchParams.get('credits');
    const amount = searchParams.get('amount');
    const orderNo = searchParams.get('order_no');

    if (payment === 'success') {
      // 显示支付成功提示
      let message = '🎉 支付成功！';
      
      if (credits) {
        message += ` 已为您添加 ${credits} 积分`;
      }
      
      if (amount) {
        message += `，支付金额 $${amount}`;
      }

      if (orderNo) {
        message += `，订单号：${orderNo}`;
      }

      toast.success(message, {
        duration: 5000,
        description: '您可以立即开始使用新增的积分来去除图片水印。',
      });

      // 刷新用户信息以更新积分显示
      refreshUserInfo();

      // 清理URL参数，避免刷新页面时重复显示
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('credits');
      url.searchParams.delete('amount');
      url.searchParams.delete('order_no');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams]);

  return null;
}
