'use client';

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Star, Zap, Crown, Building2, Briefcase } from "lucide-react";
import { useAppContext } from "@/contexts/app";
import { toast } from "sonner";
import { useTranslations } from "next-intl";

interface PricingPlan {
  id: string;
  name: string;
  description: string;
  credits: number;
  price: number;
  originalPrice?: number;
  currency: string;
  interval: 'month' | 'year' | 'one-time';
  features: string[];
  popular?: boolean;
  icon: React.ReactNode;
}

const monthlyPlans: PricingPlan[] = [
  {
    id: "basic-monthly",
    name: "基础版",
    description: "100积分/月",
    credits: 100,
    price: 7.13,
    currency: "USD",
    interval: "month",
    features: ["100积分/月", "$0.07/积分", "自动续费", "高质量去水印", "24/7客服支持"],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "standard-monthly",
    name: "标准版",
    description: "200积分/月",
    credits: 200,
    price: 10.19,
    currency: "USD",
    interval: "month",
    features: ["200积分/月", "$0.05/积分", "自动续费", "批量处理", "优先处理"],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-monthly",
    name: "专业版",
    description: "500积分/月",
    credits: 500,
    price: 15.30,
    currency: "USD",
    interval: "month",
    features: ["500积分/月", "$0.03/积分", "自动续费", "API访问", "高级客服"],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "enterprise-monthly",
    name: "企业版",
    description: "1000积分/月",
    credits: 1000,
    price: 20.40,
    currency: "USD",
    interval: "month",
    features: ["1000积分/月", "$0.02/积分", "自动续费", "团队管理", "专属客服"],
    icon: <Building2 className="w-6 h-6" />,
  },
];

const yearlyPlans: PricingPlan[] = [
  {
    id: "basic-yearly",
    name: "基础版",
    description: "100积分/月 × 12",
    credits: 1200,
    price: 45.91,
    originalPrice: 85.56,
    currency: "USD",
    interval: "year",
    features: ["1200积分/年", "$0.04/积分", "一次性年付", "高质量去水印", "24/7客服支持"],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "standard-yearly",
    name: "标准版",
    description: "200积分/月 × 12",
    credits: 2400,
    price: 71.43,
    originalPrice: 122.28,
    currency: "USD",
    interval: "year",
    features: ["2400积分/年", "$0.03/积分", "一次性年付", "批量处理", "优先处理"],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-yearly",
    name: "专业版",
    description: "500积分/月 × 12",
    credits: 6000,
    price: 101.97,
    originalPrice: 183.60,
    currency: "USD",
    interval: "year",
    features: ["6000积分/年", "$0.02/积分", "一次性年付", "API访问", "高级客服"],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "enterprise-yearly",
    name: "企业版",
    description: "1000积分/月 × 12",
    credits: 12000,
    price: 142.86,
    originalPrice: 244.80,
    currency: "USD",
    interval: "year",
    features: ["12000积分/年", "$0.01/积分", "一次性年付", "团队管理", "专属客服"],
    icon: <Crown className="w-6 h-6" />,
  },
];

const oneTimePlans: PricingPlan[] = [
  {
    id: "starter-pack",
    name: "入门包",
    description: "50积分买断",
    credits: 50,
    price: 8.15,
    currency: "USD",
    interval: "one-time",
    features: ["50积分", "$0.16/积分", "永不过期", "不自动续费", "高质量去水印"],
    icon: <Zap className="w-6 h-6" />,
  },
  {
    id: "value-pack",
    name: "超值包",
    description: "200积分买断",
    credits: 200,
    price: 25.50,
    currency: "USD",
    interval: "one-time",
    features: ["200积分", "$0.13/积分", "永不过期", "不自动续费", "批量处理"],
    popular: true,
    icon: <Star className="w-6 h-6" />,
  },
  {
    id: "pro-pack",
    name: "专业包",
    description: "500积分买断",
    credits: 500,
    price: 56.12,
    currency: "USD",
    interval: "one-time",
    features: ["500积分", "$0.11/积分", "永不过期", "不自动续费", "优先处理"],
    icon: <Crown className="w-6 h-6" />,
  },
  {
    id: "mega-pack",
    name: "超级包",
    description: "1000积分买断",
    credits: 1000,
    price: 86.73,
    currency: "USD",
    interval: "one-time",
    features: ["1000积分", "$0.09/积分", "永不过期", "不自动续费", "API访问"],
    icon: <Crown className="w-6 h-6" />,
  },
];

const PricingPage = () => {
  const [planType, setPlanType] = useState<'monthly' | 'yearly' | 'one-time'>('yearly');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null);
  const { user, setShowSignModal } = useAppContext();
  const t = useTranslations('pricing');

  const handleSubscribe = async (plan: PricingPlan) => {
    if (!user) {
      setShowSignModal(true);
      return;
    }

    setIsLoading(true);
    setLoadingPlanId(plan.id);

    try {
      // Show loading message for PayPal
      toast.loading('正在创建支付会话...', { id: 'payment-loading' });

      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan_id: plan.id,
          credits: plan.credits,
          price: plan.price,
          currency: plan.currency,
          interval: plan.interval,
          valid_months: plan.interval === 'year' ? 12 : plan.interval === 'month' ? 1 : 0,
        }),
      });

      if (response.status === 401) {
        toast.dismiss('payment-loading');
        setShowSignModal(true);
        return;
      }

      if (!response.ok) {
        toast.dismiss('payment-loading');
        throw new Error('支付请求失败');
      }

      const data = await response.json();
      if (data.code === 0) {
        toast.dismiss('payment-loading');

        if (data.data.redirect_url) {
          // PayPal payment - show redirect message
          toast.success('正在跳转到 PayPal 支付页面...', { duration: 2000 });
          setTimeout(() => {
            window.location.href = data.data.redirect_url;
          }, 1000);
        } else if (data.data.payment_url) {
          // Legacy payment URL support
          toast.success('正在跳转到支付页面...', { duration: 2000 });
          setTimeout(() => {
            window.location.href = data.data.payment_url;
          }, 1000);
        } else {
          // Development environment success
          toast.success(`订阅成功！已添加 ${plan.credits} 积分`);
          setTimeout(() => {
            window.location.reload();
          }, 1500);
        }
      } else {
        toast.dismiss('payment-loading');
        throw new Error(data.message || '支付请求失败');
      }
    } catch (error) {
      console.error('Subscribe error:', error);
      toast.dismiss('payment-loading');
      toast.error(error instanceof Error ? error.message : '支付请求失败，请稍后重试');
    } finally {
      setIsLoading(false);
      setLoadingPlanId(null);
    }
  };

  const getCurrentPlans = () => {
    switch (planType) {
      case 'monthly':
        return monthlyPlans;
      case 'yearly':
        return yearlyPlans;
      case 'one-time':
        return oneTimePlans;
      default:
        return yearlyPlans;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="w-full py-6 px-4 border-b border-white/10 backdrop-blur-sm bg-black/20">
        <div className="max-w-7xl mx-auto flex justify-center items-center">
          <img src="/logo.png" alt="Watermark Remover" className="h-10 mr-3" />
          <span className="text-2xl font-bold tracking-tight text-white">{t('title')}</span>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 py-16">
        {/* Title Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold mb-6 text-white">
            {t('title')}
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            {t('subtitle')}
          </p>

          {/* Free Plan Info */}
          <div className="bg-gradient-to-r from-green-500/20 to-blue-500/20 backdrop-blur-sm rounded-2xl p-6 border border-green-500/30 max-w-2xl mx-auto">
            <div className="flex items-center justify-center mb-4">
              <div className="bg-gradient-to-r from-green-500 to-blue-500 p-3 rounded-2xl">
                <Zap className="w-6 h-6 text-white" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-2">🎉 免费计划</h3>
            <p className="text-gray-300 mb-4">
              每月免费3次去水印机会，每日限制1次使用
            </p>
            <div className="text-sm text-gray-400">
              注册即可享受 • 无需信用卡 • 体验完整功能
            </div>
          </div>
        </div>

        {/* Plan Type Selector */}
        <div className="flex justify-center mb-12">
          <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-2 border border-white/10">
            <div className="flex space-x-2">
              {[
                { key: 'monthly', label: '💳 按月自动续费', badge: null },
                { key: 'yearly', label: '🗓️ 按年一次性付费', badge: '最划算' },
                { key: 'one-time', label: '💰 一次性购买', badge: '买断' },
              ].map((option) => (
                <button
                  key={option.key}
                  onClick={() => setPlanType(option.key as any)}
                  className={`relative px-6 py-3 rounded-xl font-semibold transition-all duration-200 ${
                    planType === option.key
                      ? 'bg-white text-gray-900 shadow-lg'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  {option.label}
                  {option.badge && (
                    <Badge className="absolute -top-2 -right-2 bg-gradient-to-r from-green-400 to-blue-500 text-white text-xs">
                      {option.badge}
                    </Badge>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-12">
          {getCurrentPlans().map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-black/40 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
                plan.popular
                  ? 'border-purple-500 shadow-purple-500/20 shadow-2xl'
                  : 'border-white/10 hover:border-white/20'
              }`}
            >
              {/* Popular Badge */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-1 text-xs font-semibold">
                    最受欢迎
                  </Badge>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-6">
                <div className="flex justify-center mb-3">
                  <div className={`p-2 rounded-xl ${
                    plan.popular
                      ? 'bg-gradient-to-r from-purple-500 to-pink-500'
                      : 'bg-gradient-to-r from-blue-500 to-cyan-500'
                  }`}>
                    {plan.icon}
                  </div>
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-3 text-sm">{plan.description}</p>

                {/* Price */}
                <div className="mb-4">
                  {plan.originalPrice && (
                    <div className="text-gray-500 line-through text-base mb-1">
                      ${plan.originalPrice}
                    </div>
                  )}
                  <div className="text-3xl font-bold text-white mb-1">
                    ${plan.price}
                  </div>
                  <div className="text-gray-400 text-xs">
                    {plan.interval === 'month' && '/ 月'}
                    {plan.interval === 'year' && '/ 年'}
                    {plan.interval === 'one-time' && '一次性'}
                  </div>
                </div>

                {/* Credits */}
                <div className="bg-white/5 rounded-lg p-2 mb-4">
                  <div className="text-xl font-bold text-white">{plan.credits}</div>
                  <div className="text-gray-400 text-xs">积分</div>
                </div>
              </div>

              {/* Features */}
              <div className="mb-6">
                <ul className="space-y-2">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-300 text-sm">
                      <Check className="w-4 h-4 text-green-400 mr-2 flex-shrink-0" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Subscribe Button */}
              <Button
                onClick={() => handleSubscribe(plan)}
                disabled={isLoading}
                className={`w-full py-3 px-4 text-sm font-semibold rounded-lg transition-all duration-200 ${
                  plan.popular
                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white shadow-lg'
                    : 'bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-md'
                } ${isLoading && loadingPlanId === plan.id ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isLoading && loadingPlanId === plan.id ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                    <span>创建支付会话...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <span>
                      {plan.interval === 'one-time' ? '通过 PayPal 购买' : '通过 PayPal 订阅'}
                    </span>
                    <img src="/imgs/logos/paypal.png" alt="PayPal" className="h-4 opacity-90" />
                  </div>
                )}
              </Button>
            </div>
          ))}
        </div>

        {/* Payment Methods */}
        <div className="text-center">
          <div className="mb-6">
            <h3 className="text-xl font-semibold text-white mb-4">安全支付方式</h3>
            <div className="flex items-center justify-center gap-4">
              {/* PayPal Payment Method - Highlighted as Primary */}
              <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm rounded-xl p-6 border border-blue-500/30 relative">
                <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-2 py-1 text-xs">
                    推荐
                  </Badge>
                </div>
                <img src="/imgs/logos/paypal.png" alt="PayPal" className="h-10 mx-auto mb-2" />
                <div className="text-white text-sm font-medium">PayPal</div>
                <div className="text-gray-300 text-xs">全球信赖</div>
              </div>

              {/* Alternative Payment Methods */}
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/10 opacity-60">
                <div className="text-white font-semibold text-sm">信用卡</div>
                <div className="text-gray-400 text-xs">Visa • MasterCard</div>
              </div>
            </div>

            {/* Payment Features */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 max-w-2xl mx-auto">
              <div className="flex items-center justify-center gap-2 text-gray-300 text-sm">
                <Check className="w-4 h-4 text-green-400" />
                <span>SSL 加密保护</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-gray-300 text-sm">
                <Check className="w-4 h-4 text-green-400" />
                <span>30天退款保证</span>
              </div>
              <div className="flex items-center justify-center gap-2 text-gray-300 text-sm">
                <Check className="w-4 h-4 text-green-400" />
                <span>24/7 客服支持</span>
              </div>
            </div>
          </div>
          <p className="text-gray-400 text-sm">
            所有支付均通过 PayPal 安全处理，您的财务信息完全受保护
          </p>
        </div>
      </main>

      {/* Footer */}
      <footer className="border-t border-white/10 bg-black/20 backdrop-blur-sm py-8">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <p className="text-gray-400">
            &copy; {new Date().getFullYear()} Watermark Remover. Powered by Next.js.
          </p>
        </div>
      </footer>
    </div>
  );
};

export default PricingPage;
