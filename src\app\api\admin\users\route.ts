import { respData, respErr } from "@/lib/resp";
import { getUsers, getUsersTotal } from "@/models/user";
import { isCurrentUserAdmin } from "@/services/admin";

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { page = 1, limit = 20 } = await req.json();

    // 获取用户列表
    const users = await getUsers(page, limit);
    const total = await getUsersTotal();

    return respData({
      users: users || [],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (e) {
    console.log("get admin users failed: ", e);
    return respErr("获取用户列表失败");
  }
}
