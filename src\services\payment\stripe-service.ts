import Stripe from 'stripe';
import { 
  IPaymentService, 
  CreatePaymentSessionParams, 
  PaymentSessionResponse, 
  PaymentCallbackSession,
  PaymentProvider 
} from '@/types/payment';
import { PaymentConfig } from './config';

/**
 * Stripe支付服务实现
 * 遵循SRP原则：只负责Stripe支付相关操作
 */
export class StripePaymentService implements IPaymentService {
  private stripe: Stripe;
  private config: PaymentConfig;

  constructor() {
    this.config = PaymentConfig.getInstance();
    const stripeConfig = this.config.getStripeConfig();
    this.stripe = new Stripe(stripeConfig.privateKey);
  }

  /**
   * 创建Stripe支付会话
   */
  async createPaymentSession(params: CreatePaymentSessionParams): Promise<PaymentSessionResponse> {
    try {
      const stripeConfig = this.config.getStripeConfig();
      
      let options: Stripe.Checkout.SessionCreateParams = {
        payment_method_types: ["card"],
        line_items: [
          {
            price_data: {
              currency: params.currency,
              product_data: {
                name: params.product_name,
              },
              unit_amount: params.amount,
              recurring: params.is_subscription
                ? {
                    interval: params.interval as Stripe.Price.Recurring.Interval,
                  }
                : undefined,
            },
            quantity: 1,
          },
        ],
        allow_promotion_codes: true,
        metadata: {
          project: process.env.NEXT_PUBLIC_PROJECT_NAME || "",
          product_name: params.product_name,
          order_no: params.order_no.toString(),
          user_email: params.user_email,
          credits: params.credits.toString(),
          user_uuid: params.user_uuid,
        },
        mode: params.is_subscription ? "subscription" : "payment",
        success_url: params.success_url,
        cancel_url: params.cancel_url,
        customer_email: params.user_email,
      };

      if (params.is_subscription) {
        options.subscription_data = {
          metadata: options.metadata,
        };
      }

      // 支持中国支付方式
      if (params.currency === "cny") {
        options.payment_method_types = ["wechat_pay", "alipay", "card"];
        options.payment_method_options = {
          wechat_pay: {
            client: "web",
          },
          alipay: {},
        };
      }

      const session = await this.stripe.checkout.sessions.create(options);

      return {
        session_id: session.id,
        public_key: stripeConfig.publicKey,
        order_no: params.order_no,
      };
    } catch (error) {
      console.error('Stripe payment session creation failed:', error);
      throw new Error(`Failed to create Stripe payment session: ${error}`);
    }
  }

  /**
   * 获取Stripe支付会话详情
   */
  async getPaymentSession(session_id: string): Promise<PaymentCallbackSession> {
    try {
      const session = await this.stripe.checkout.sessions.retrieve(session_id);
      
      return {
        id: session.id,
        payment_status: session.payment_status || 'unpaid',
        customer_email: session.customer_email || undefined,
        customer_details: session.customer_details ? {
          email: session.customer_details.email || undefined
        } : undefined,
        metadata: session.metadata || undefined,
        amount_total: session.amount_total || undefined,
        currency: session.currency || undefined,
      };
    } catch (error) {
      console.error('Failed to retrieve Stripe session:', error);
      throw new Error(`Failed to retrieve Stripe session: ${error}`);
    }
  }

  /**
   * 验证Stripe webhook签名
   */
  verifyWebhookSignature(payload: string, signature: string, secret: string): any {
    try {
      return this.stripe.webhooks.constructEvent(payload, signature, secret);
    } catch (error) {
      console.error('Stripe webhook signature verification failed:', error);
      throw new Error(`Stripe webhook signature verification failed: ${error}`);
    }
  }

  /**
   * 获取支付服务提供商名称
   */
  getProviderName(): PaymentProvider {
    return PaymentProvider.STRIPE;
  }
}
