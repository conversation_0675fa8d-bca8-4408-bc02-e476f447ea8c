'use client';

import React, { useRef, useState } from "react";

const SUPPORTED_FORMATS = ["png", "jpeg", "jpg", "webp", "heic"];
const MAX_RESOLUTION = "5000 x 5000 px";
const MAX_SIZE_MB = 25;

const RemoveWatermarkPage = () => {
  // 状态管理
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [resultUrl, setResultUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 触发文件选择
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (f) {
      setFile(f);
      setPreviewUrl(URL.createObjectURL(f));
      setResultUrl(null);
      setError(null);
    }
  };

  // 去水印处理
  const handleRemoveWatermark = async () => {
    if (!file) return;
    setProcessing(true);
    setError(null);
    setResultUrl(null);
    try {
      const formData = new FormData();
      formData.append("file", file);
      const res = await fetch("/api/remove-watermark", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      if (data.image) {
        setResultUrl(`data:image/png;base64,${data.image}`);
      } else {
        setError(data.error || "去水印失败");
      }
    } catch (e) {
      setError("网络错误或服务异常");
    } finally {
      setProcessing(false);
    }
  };

  // 下载处理后图片
  const handleDownload = () => {
    if (resultUrl) {
      const a = document.createElement("a");
      a.href = resultUrl;
      a.download = file?.name?.replace(/\.(\w+)$/, "_no_watermark.png") || "result.png";
      a.click();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 via-pink-100 to-purple-100 flex flex-col items-center">
      {/* 顶部导航 */}
      <header className="w-full flex items-center justify-between px-8 py-4 bg-white/80 shadow-sm fixed top-0 left-0 z-20 backdrop-blur">
        <div className="flex items-center gap-2">
          {/* logo 建议用SVG或上传新图片 */}
          <img src="/logo.png" alt="Watermark Remover" className="h-8" />
          <span className="text-xl font-bold text-blue-700 tracking-tight">Watermark Remover</span>
        </div>
        <nav className="flex gap-8 text-gray-700 text-base font-medium">
          <a href="/">Home</a>
          <a href="/dashboard">User Center</a>
        </nav>
      </header>
      {/* 主流程区 */}
      <main className="flex-1 w-full flex flex-col items-center justify-center pt-32 pb-12 px-4">
        <section className="w-full max-w-xl flex flex-col items-center text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-extrabold mb-4 text-blue-900 drop-shadow">AI Watermark Remover</h1>
          <p className="text-lg text-gray-600 mb-6">Upload your image and remove watermarks instantly with AI.</p>
          <div
            className="w-full bg-white/90 rounded-2xl shadow-xl p-8 flex flex-col items-center border border-blue-100 cursor-pointer hover:shadow-2xl transition group"
            onClick={triggerFileInput} // 新增：点击整个上传卡片也能触发上传
          >
            {!file && (
              <>
                <button
                  className="w-full h-48 border-4 border-dashed border-blue-200 rounded-2xl flex flex-col items-center justify-center text-blue-400 hover:border-blue-400 transition mb-4 bg-gradient-to-br from-blue-100 via-pink-100 to-purple-100 group-hover:scale-105 group-hover:shadow-lg"
                  onClick={e => { e.stopPropagation(); triggerFileInput(); }} // 新增：点击按钮也能触发上传
                >
                  <div className="flex flex-col items-center">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-400 via-pink-400 to-purple-500 flex items-center justify-center mb-2 shadow-lg group-hover:scale-110 transition-transform">
                      <svg width="32" height="32" fill="none" viewBox="0 0 24 24"><path fill="#fff" d="M12 3v12.586l4.95-4.95 1.414 1.414L12 19.414l-6.364-6.364 1.414-1.414 4.95 4.95V3h2z"/></svg>
                    </div>
                    <span className="text-xl font-semibold">Drop your image here or click to upload</span>
                    <span className="text-xs mt-2 text-blue-400">Supported: {SUPPORTED_FORMATS.join(", ")}</span>
                  </div>
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
              </>
            )}
            {file && (
              <>
                <div className="mb-4 w-full flex flex-col items-center">
                  <div className="w-full flex justify-center">
                    <img
                      src={previewUrl!}
                      alt="预览"
                      className="max-h-64 rounded-xl shadow-lg mb-2 border-2 border-blue-200 bg-white animate-fade-in"
                      style={{ maxWidth: "100%" }}
                    />
                  </div>
                  <div className="text-sm text-gray-500">{file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</div>
                </div>
                {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
                {!resultUrl && (
                  <button
                    className="w-full py-3 px-4 bg-gradient-to-r from-blue-500 via-pink-400 to-purple-500 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-purple-700 transition mb-2 shadow-lg text-lg disabled:opacity-60 disabled:cursor-not-allowed"
                    onClick={handleRemoveWatermark}
                    disabled={processing}
                  >
                    {processing ? (
                      <span className="flex items-center justify-center gap-2"><span className="loader border-white border-t-blue-400"></span> 正在去除水印...</span>
                    ) : (
                      "去除水印"
                    )}
                  </button>
                )}
                {resultUrl && (
                  <>
                    <div className="mb-4 w-full flex flex-col items-center">
                      <img
                        src={resultUrl}
                        alt="去水印结果"
                        className="max-h-64 rounded-xl shadow-lg mb-2 border-2 border-green-400 bg-white animate-fade-in"
                        style={{ maxWidth: "100%" }}
                      />
                    </div>
                    <button
                      className="w-full py-3 px-4 bg-gradient-to-r from-green-500 via-blue-400 to-purple-500 text-white rounded-xl font-semibold hover:from-green-600 hover:to-purple-700 transition mb-2 shadow-lg text-lg"
                      onClick={handleDownload}
                    >
                      下载去水印图片
                    </button>
                  </>
                )}
                <button
                  className="w-full py-2 px-4 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition shadow mt-2"
                  onClick={() => { setFile(null); setPreviewUrl(null); setResultUrl(null); setError(null); }}
                  disabled={processing}
                >
                  重新选择图片
                </button>
              </>
            )}
          </div>
          <div className="flex gap-2 mt-4 text-xs text-gray-500 justify-center">
            <span>✔ 100% Quick</span>
            <span>✔ Private</span>
            <span>✔ Simple</span>
          </div>
        </section>
      </main>
      {/* 底部信息区 */}
      <footer className="w-full py-8 text-center text-gray-400 text-xs border-t bg-blue-950/90 mt-10">
        <div className="max-w-5xl mx-auto flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="flex items-center gap-2">
            <img src="/logo.png" alt="Watermark Remover" className="h-6" />
            <span className="font-bold text-white">Watermark Remover</span>
          </div>
          <div className="flex gap-6 text-gray-300">
            <a href="/privacy-policy" className="hover:text-white underline">Privacy Policy</a>
            <a href="/terms-of-service" className="hover:text-white underline">Terms of Service</a>
            <span>© {new Date().getFullYear()} Watermark Remover. All rights reserved.</span>
          </div>
        </div>
      </footer>
      {/* 动画样式 */}
      <style jsx global>{`
        @keyframes fade-in {
          from { opacity: 0; transform: scale(0.98); }
          to { opacity: 1; transform: scale(1); }
        }
        .animate-fade-in {
          animation: fade-in 0.5s ease;
        }
        .loader {
          border: 3px solid #fff;
          border-radius: 50%;
          border-top: 3px solid #60a5fa;
          width: 1.2em;
          height: 1.2em;
          animation: spin 0.8s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default RemoveWatermarkPage;
