import DashboardLayout from "@/components/dashboard/layout";
import Empty from "@/components/blocks/empty";
import { ReactNode } from "react";
import { Sidebar } from "@/types/blocks/sidebar";
import { getUserInfo } from "@/services/user";
import { isCurrentUserAdmin } from "@/services/admin";
import { redirect } from "next/navigation";

export default async function AdminLayout({
  children,
}: {
  children: ReactNode;
}) {
  const userInfo = await getUserInfo();
  if (!userInfo || !userInfo.email) {
    redirect("/auth/signin");
  }

  // 检查管理员权限
  const isAdmin = await isCurrentUserAdmin();
  if (!isAdmin) {
    return <Empty message="No access" />;
  }

  const sidebar: Sidebar = {
    brand: {
      title: "ShipAny",
      logo: {
        src: "/logo.png",
        alt: "ShipAny",
      },
      url: "/admin",
    },
    nav: {
      items: [
        {
          title: "Dashboard",
          url: "/admin",
          icon: "RiDashboardLine",
        },
      ],
    },
    library: {
      title: "Menu",
      items: [
        {
          title: "Users",
          url: "/admin/users",
          icon: "RiUserLine",
        },
        {
          title: "Orders",
          icon: "RiOrderPlayLine",
          url: "/admin/orders",
        },
        {
          title: "Posts",
          url: "/admin/posts",
          icon: "RiArticleLine",
        },
        {
          title: "Feedbacks",
          url: "/admin/feedbacks",
          icon: "RiMessage2Line",
        },
      ],
    },
    bottomNav: {
      items: [
        {
          title: "Home",
          url: "/",
          target: "_self",
          icon: "RiHomeLine",
        },
        {
          title: "Pricing",
          url: "/pricing",
          target: "_self",
          icon: "RiPriceTag3Line",
        },
        {
          title: "Contact",
          url: "/contact",
          target: "_self",
          icon: "RiContactsLine",
        },
      ],
    },
    social: {
      items: [
        {
          title: "Home",
          url: "/",
          target: "_self",
          icon: "RiHomeLine",
        },
        {
          title: "Pricing",
          url: "/pricing",
          target: "_self",
          icon: "RiPriceTag3Line",
        },
        {
          title: "Contact",
          url: "/contact",
          target: "_self",
          icon: "RiContactsLine",
        },
      ],
    },
    account: {
      items: [
        {
          title: "Home",
          url: "/",
          icon: "RiHomeLine",
          target: "_blank",
        },
        {
          title: "Recharge",
          url: "/pricing",
          icon: "RiMoneyDollarBoxLine",
          target: "_blank",
        },
      ],
    },
  };

  return <DashboardLayout sidebar={sidebar}>{children}</DashboardLayout>;
}
