# ShipAny Template One - 项目说明文档

## 项目概述

ShipAny Template One 是一个现代化的 AI SaaS 创业项目模板，基于 Next.js 14+ 构建，旨在帮助开发者在几小时内快速构建和部署 AI SaaS 应用。该模板集成了完整的用户认证、支付系统、多语言支持、AI 服务集成等企业级功能。

### 核心特性

- 🚀 **快速启动**: 基于 Next.js 14+ App Router，支持 TypeScript
- 🎨 **现代 UI**: 使用 Tailwind CSS + Shadcn/ui 组件库
- 🌍 **国际化**: 支持多语言（英文、中文），基于 next-intl
- 🔐 **认证系统**: 集成 NextAuth.js，支持 Google、GitHub 登录
- 💳 **多支付集成**: 支持 Stripe 和 PayPal 双支付系统，支持订阅和一次性付款
- 🤖 **AI 集成**: 支持多个 AI 提供商（OpenAI、DeepSeek、Replicate 等）
- 📊 **管理后台**: 完整的管理面板，包含数据统计和用户管理
- 🗄️ **数据库**: 使用 Drizzle ORM + PostgreSQL
- 📱 **响应式**: 完全响应式设计，支持移动端
- 🎯 **SEO 优化**: 内置 SEO 优化和分析工具集成

### 技术栈

**前端框架**
- Next.js 15.1.3 (App Router)
- React 19
- TypeScript 5.7.2
- Tailwind CSS 4.1.4

**UI 组件**
- Shadcn/ui 组件库
- Radix UI 基础组件
- Lucide React 图标
- Framer Motion 动画

**后端服务**
- Next.js API Routes
- NextAuth.js 认证
- Drizzle ORM 数据库操作
- PostgreSQL 数据库

**AI 集成**
- AI SDK (Vercel)
- OpenAI API
- DeepSeek API
- Replicate API
- 自定义 AI 提供商支持

**支付系统**
- 双支付系统（Stripe + PayPal）
- 统一支付接口设计
- 订阅和一次性付款
- Webhook 事件处理
- 多币种支持

**开发工具**
- PNPM 包管理
- ESLint 代码检查
- Drizzle Kit 数据库迁移
- Docker 容器化

## 快速开始指南

### 环境要求

- Node.js 18+
- PNPM 8+
- PostgreSQL 数据库
- Git

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/shipanyai/shipany-template-one.git
cd shipany-template-one
```

2. **安装依赖**
```bash
pnpm install
```

3. **环境配置**
```bash
cp .env.example .env.development
```

编辑 `.env.development` 文件，配置必要的环境变量：

```bash
# 基础配置
NEXT_PUBLIC_WEB_URL="http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME="ShipAny"

# 数据库配置 (Supabase 推荐)
DATABASE_URL="postgresql://username:password@localhost:5432/database"

# 认证配置
AUTH_SECRET="your-auth-secret"
AUTH_URL="http://localhost:3000/api/auth"

# Google 认证 (可选)
AUTH_GOOGLE_ID="your-google-client-id"
AUTH_GOOGLE_SECRET="your-google-client-secret"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="true"

# 支付系统配置（选择一种）
PAYMENT_PROVIDER="stripe"  # 或 "paypal"

# Stripe 配置（当 PAYMENT_PROVIDER=stripe 时）
STRIPE_PUBLIC_KEY="pk_test_..."
STRIPE_PRIVATE_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# PayPal 配置（当 PAYMENT_PROVIDER=paypal 时）
PAYPAL_CLIENT_ID="your_client_id"
PAYPAL_CLIENT_SECRET="your_client_secret"
PAYPAL_ENVIRONMENT="sandbox"  # sandbox 或 live
```

4. **数据库设置**
```bash
# 生成数据库迁移文件
pnpm db:generate

# 执行数据库迁移
pnpm db:migrate

# 可选：启动数据库管理界面
pnpm db:studio
```

5. **启动开发服务器**
```bash
pnpm dev
```

访问 http://localhost:3000 查看应用。

### Docker 部署

```bash
# 构建 Docker 镜像
pnpm docker:build

# 运行容器
docker run -p 3000:3000 shipany-template-one:latest
```

## 项目架构说明

### 目录结构

```
shipany-template-one/
├── src/
│   ├── app/                    # Next.js App Router 页面和 API
│   │   ├── [locale]/          # 多语言路由
│   │   │   ├── (default)/     # 默认布局页面
│   │   │   ├── (admin)/       # 管理后台页面
│   │   │   └── (dashboard)/   # 用户面板页面
│   │   ├── api/               # API 路由
│   │   │   ├── checkout/      # 支付相关 API
│   │   │   ├── demo/          # AI 功能演示 API
│   │   │   └── ...
│   │   ├── globals.css        # 全局样式
│   │   ├── layout.tsx         # 根布局
│   │   └── theme.css          # 主题配置
│   ├── components/            # React 组件
│   │   ├── blocks/            # 页面区块组件
│   │   │   ├── header/        # 页头组件
│   │   │   ├── footer/        # 页脚组件
│   │   │   ├── pricing/       # 定价组件
│   │   │   └── ...
│   │   ├── ui/                # 基础 UI 组件 (Shadcn/ui)
│   │   ├── dashboard/         # 仪表板组件
│   │   └── ...
│   ├── contexts/              # React Context
│   │   └── app.tsx            # 应用全局状态
│   ├── db/                    # 数据库相关
│   │   ├── schema.ts          # 数据库模式定义
│   │   ├── config.ts          # Drizzle 配置
│   │   └── migrations/        # 数据库迁移文件
│   ├── i18n/                  # 国际化
│   │   ├── messages/          # 翻译文件
│   │   ├── pages/             # 页面内容配置
│   │   └── ...
│   ├── lib/                   # 工具函数
│   │   ├── utils.ts           # 通用工具
│   │   ├── storage.ts         # 文件存储
│   │   └── ...
│   ├── models/                # 数据模型
│   │   ├── user.ts            # 用户模型
│   │   ├── order.ts           # 订单模型
│   │   └── ...
│   ├── services/              # 业务逻辑服务
│   │   ├── user.ts            # 用户服务
│   │   ├── credit.ts          # 积分服务
│   │   └── ...
│   ├── types/                 # TypeScript 类型定义
│   └── auth/                  # 认证配置
├── public/                    # 静态资源
├── docs/                      # 项目文档
├── Dockerfile                 # Docker 配置
├── next.config.mjs            # Next.js 配置
├── package.json               # 项目依赖
└── tailwind.config.ts         # Tailwind 配置
```

### 核心模块说明

#### 1. 认证系统 (`src/auth/`)
- 基于 NextAuth.js 实现
- 支持多种登录方式：Google、GitHub、凭证登录
- 自动用户注册和积分分配
- JWT Token 管理

#### 2. 数据库层 (`src/db/`, `src/models/`)
- 使用 Drizzle ORM 进行数据库操作
- PostgreSQL 数据库
- 主要数据表：
  - `users`: 用户信息
  - `orders`: 订单记录
  - `credits`: 积分系统
  - `apikeys`: API 密钥管理
  - `posts`: 内容管理
  - `feedbacks`: 用户反馈
  - `affiliates`: 推荐系统

#### 3. 业务服务层 (`src/services/`)
- 用户管理服务
- 积分系统服务
- 订单处理服务
- 页面内容服务

#### 4. AI 集成 (`src/aisdk/`)
- 支持多个 AI 提供商
- 统一的 AI SDK 接口
- 文本生成、图像生成功能
- 自定义 AI 提供商扩展

#### 5. 支付系统 (`src/services/payment/`)
- 双支付系统（Stripe + PayPal）
- 统一支付服务接口设计
- 支付服务工厂模式
- 支持一次性付款和订阅
- 多币种支持（USD、CNY）
- Webhook 事件处理
- 遵循 DRY、SRP、Clean Code 原则

#### 6. 国际化 (`src/i18n/`)
- 基于 next-intl 实现
- 支持英文、中文
- 页面内容和 UI 文本分离
- 动态语言切换

## API 文档

### 认证相关 API

#### 获取用户信息
```http
POST /api/get-user-info
Content-Type: application/json

Response:
{
  "code": 0,
  "data": {
    "uuid": "user-uuid",
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "credits": 100,
    "avatar_url": "头像URL"
  }
}
```

### 支付相关 API

#### 创建支付会话
```http
POST /api/checkout
Content-Type: application/json

{
  "product_id": "basic_plan",
  "amount": 999,
  "currency": "usd",
  "interval": "month",
  "credits": 1000
}

Response:
{
  "code": 0,
  "data": {
    "session_id": "cs_test_...",
    "order_no": "order_123456"
  }
}
```

### AI 功能 API

#### 文本生成
```http
POST /api/demo/gen-text
Content-Type: application/json

{
  "prompt": "生成一段关于AI的介绍",
  "provider": "openai",
  "model": "gpt-4"
}

Response:
{
  "code": 0,
  "data": {
    "text": "生成的文本内容...",
    "usage": {
      "prompt_tokens": 10,
      "completion_tokens": 50
    }
  }
}
```

#### 图像生成
```http
POST /api/demo/gen-image
Content-Type: application/json

{
  "prompt": "一只可爱的猫咪",
  "provider": "openai",
  "model": "dall-e-3"
}

Response:
{
  "code": 0,
  "data": {
    "image_url": "生成的图像URL",
    "storage_url": "存储的图像URL"
  }
}
```

### 反馈系统 API

#### 提交反馈
```http
POST /api/add-feedback
Content-Type: application/json

{
  "content": "反馈内容",
  "rating": 5
}

Response:
{
  "code": 0,
  "data": {
    "id": 1,
    "status": "created"
  }
}
```

## 开发指南

### 本地开发

1. **启动开发服务器**
```bash
pnpm dev
```

2. **数据库操作**
```bash
# 生成迁移文件
pnpm db:generate

# 执行迁移
pnpm db:migrate

# 推送模式更改（开发环境）
pnpm db:push

# 启动数据库管理界面
pnpm db:studio
```

3. **代码检查**
```bash
pnpm lint
```

4. **构建分析**
```bash
pnpm analyze
```

### 自定义配置

#### 1. 主题定制
编辑 `src/app/theme.css` 文件来自定义主题颜色：

```css
:root {
  --primary: oklch(0.67 0.16 245);
  --secondary: oklch(0.19 0.01 248.51);
  /* 更多颜色变量... */
}
```

推荐使用 [tweakcn](https://tweakcn.com/editor/theme) 在线主题编辑器。

#### 2. 页面内容配置
编辑 `src/i18n/pages/landing/` 目录下的 JSON 文件来配置页面内容：

```json
{
  "header": {
    "brand": {
      "title": "你的品牌名",
      "logo": {
        "src": "/your-logo.png"
      }
    },
    "nav": {
      "items": [
        {
          "title": "功能",
          "url": "/#features"
        }
      ]
    }
  }
}
```

#### 3. 添加新的 AI 提供商
在 `src/aisdk/` 目录下创建新的提供商配置：

```typescript
// src/aisdk/your-provider/index.ts
import { createProvider } from '@ai-sdk/provider';

export const yourProvider = createProvider({
  id: 'your-provider',
  baseURL: 'https://api.your-provider.com',
  // 其他配置...
});
```

### 测试

项目包含了 API 测试文件 `debug/apitest.http`，可以使用 VS Code 的 REST Client 扩展进行测试。

### 部署

#### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

#### Cloudflare Pages 部署

1. 配置环境变量
```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

2. 部署
```bash
npm run cf:deploy
```

#### Docker 部署

```bash
# 构建镜像
docker build -t shipany-template-one .

# 运行容器
docker run -p 3000:3000 \
  -e DATABASE_URL="your-database-url" \
  -e AUTH_SECRET="your-auth-secret" \
  shipany-template-one
```

## 配置说明

### 环境变量详解

#### 基础配置
- `NEXT_PUBLIC_WEB_URL`: 应用的公开访问 URL
- `NEXT_PUBLIC_PROJECT_NAME`: 项目名称，用于品牌显示

#### 数据库配置
- `DATABASE_URL`: PostgreSQL 数据库连接字符串

#### 认证配置
- `AUTH_SECRET`: NextAuth.js 密钥，用于 JWT 签名
- `AUTH_URL`: 认证服务的 URL
- `AUTH_GOOGLE_ID/SECRET`: Google OAuth 应用凭证
- `AUTH_GITHUB_ID/SECRET`: GitHub OAuth 应用凭证

#### 支付配置
- `PAYMENT_PROVIDER`: 支付提供商选择（stripe/paypal）
- `STRIPE_PUBLIC_KEY`: Stripe 公钥
- `STRIPE_PRIVATE_KEY`: Stripe 私钥
- `STRIPE_WEBHOOK_SECRET`: Stripe Webhook 密钥
- `PAYPAL_CLIENT_ID`: PayPal 客户端 ID
- `PAYPAL_CLIENT_SECRET`: PayPal 客户端密钥
- `PAYPAL_ENVIRONMENT`: PayPal 环境（sandbox/live）

#### 存储配置
- `STORAGE_ENDPOINT`: S3 兼容存储端点
- `STORAGE_ACCESS_KEY/SECRET_KEY`: 存储访问凭证
- `STORAGE_BUCKET`: 存储桶名称

#### 分析配置
- `NEXT_PUBLIC_GOOGLE_ANALYTICS_ID`: Google Analytics ID
- `NEXT_PUBLIC_OPENPANEL_CLIENT_ID`: OpenPanel 客户端 ID
- `NEXT_PUBLIC_PLAUSIBLE_DOMAIN`: Plausible 域名

### 数据库模式

#### 用户表 (users)
```sql
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  uuid VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255) NOT NULL,
  nickname VARCHAR(255),
  avatar_url VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE,
  -- 更多字段...
);
```

#### 订单表 (orders)
```sql
CREATE TABLE orders (
  id SERIAL PRIMARY KEY,
  order_no VARCHAR(255) UNIQUE NOT NULL,
  user_uuid VARCHAR(255) NOT NULL,
  amount INTEGER NOT NULL,
  status VARCHAR(50) NOT NULL,
  stripe_session_id VARCHAR(255),
  -- 更多字段...
);
```

#### 积分表 (credits)
```sql
CREATE TABLE credits (
  id SERIAL PRIMARY KEY,
  trans_no VARCHAR(255) UNIQUE NOT NULL,
  user_uuid VARCHAR(255) NOT NULL,
  trans_type VARCHAR(50) NOT NULL,
  credits INTEGER NOT NULL,
  expired_at TIMESTAMP WITH TIME ZONE,
  -- 更多字段...
);
```

## 故障排除和常见问题

### 常见问题

#### 1. 数据库连接失败
**问题**: `Error: connect ECONNREFUSED`
**解决方案**:
- 检查 `DATABASE_URL` 是否正确配置
- 确保数据库服务正在运行
- 验证网络连接和防火墙设置

#### 2. 认证失败
**问题**: Google/GitHub 登录不工作
**解决方案**:
- 检查 OAuth 应用配置
- 验证回调 URL 设置
- 确保环境变量正确配置

#### 3. 支付失败
**问题**: 支付处理失败
**解决方案**:
- 检查支付提供商密钥配置
- 验证 Webhook 端点设置
- 查看支付服务商仪表板的错误日志
- 确保 `PAYMENT_PROVIDER` 环境变量正确设置

#### 4. 构建失败
**问题**: Next.js 构建错误
**解决方案**:
- 清除缓存：`rm -rf .next node_modules && pnpm install`
- 检查 TypeScript 类型错误
- 验证环境变量在构建时的可用性

### 调试技巧

1. **启用详细日志**
```bash
NODE_ENV=development pnpm dev
```

2. **数据库查询调试**
```bash
# 启动数据库管理界面
pnpm db:studio
```

3. **API 调试**
使用 `debug/apitest.http` 文件测试 API 端点

4. **支付调试**
- Stripe: 使用测试模式和测试卡号 `4242424242424242`
- PayPal: 使用沙盒环境和测试账户

### 性能优化

1. **图像优化**
- 使用 Next.js Image 组件
- 配置适当的图像格式和尺寸

2. **代码分割**
- 使用动态导入 `import()`
- 按路由分割代码

3. **缓存策略**
- 配置适当的 HTTP 缓存头
- 使用 Next.js 内置缓存

## 贡献指南

### 开发流程

1. **Fork 项目**
2. **创建功能分支**
```bash
git checkout -b feature/your-feature-name
```

3. **提交更改**
```bash
git commit -m "feat: add your feature description"
```

4. **推送分支**
```bash
git push origin feature/your-feature-name
```

5. **创建 Pull Request**

### 代码规范

- 使用 TypeScript 进行类型安全
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 组件名使用 PascalCase
- 文件名使用 kebab-case

### 提交信息规范

使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范：

- `feat:` 新功能
- `fix:` 错误修复
- `docs:` 文档更新
- `style:` 代码格式调整
- `refactor:` 代码重构
- `test:` 测试相关
- `chore:` 构建过程或辅助工具的变动

## 许可证

本项目使用 [ShipAny AI SaaS Boilerplate License Agreement](../LICENSE) 许可证。

## 社区和支持

- **官方网站**: [ShipAny](https://shipany.ai)
- **文档**: [docs.shipany.ai](https://docs.shipany.ai)
- **GitHub**: [shipanyai/shipany-template-one](https://github.com/shipanyai/shipany-template-one)

## 更新日志

### v2.7.0 (当前版本)
- 🎉 **新增 PayPal 支付集成**
  - 实现双支付系统（Stripe + PayPal）
  - 统一支付服务接口设计
  - 支付服务工厂模式
  - 遵循 DRY、SRP、Clean Code 原则
- 📊 **数据库架构优化**
  - 新增支付提供商字段
  - 支持多支付方式订单管理
- 🛠️ **开发工具增强**
  - 新增支付配置测试脚本
  - 完善错误处理和调试功能

### v2.6.0
- 升级到 Next.js 15.1.3
- 新增 AI 图像生成功能
- 优化支付流程
- 改进国际化支持
- 增强管理后台功能

---

*最后更新: 2025-01-11*
