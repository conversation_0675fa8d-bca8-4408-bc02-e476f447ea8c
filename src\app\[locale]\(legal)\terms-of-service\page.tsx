import { getTranslations } from "next-intl/server";

export default async function TermsOfServicePage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal.terms' });

  return (
    <div className="prose prose-slate dark:prose-invert max-w-none">
      <h1 className="text-4xl font-bold mb-8 text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
        {t('title')}
      </h1>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('introduction.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('introduction.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('service_use.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('service_use.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('user_accounts.title')}
        </h2>
        <div className="space-y-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2 text-blue-800 dark:text-blue-300">
              {t('user_accounts.account_creation.title')}
            </h3>
            <p className="text-gray-700 dark:text-gray-300">
              {t('user_accounts.account_creation.content')}
            </p>
          </div>
          
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2 text-green-800 dark:text-green-300">
              {t('user_accounts.account_security.title')}
            </h3>
            <p className="text-gray-700 dark:text-gray-300">
              {t('user_accounts.account_security.content')}
            </p>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2 text-purple-800 dark:text-purple-300">
              {t('user_accounts.user_responsibilities.title')}
            </h3>
            <p className="text-gray-700 dark:text-gray-300">
              {t('user_accounts.user_responsibilities.content')}
            </p>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('intellectual_property.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('intellectual_property.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('prohibited_uses.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
          {t('prohibited_uses.intro')}
        </p>
        <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
          <li>{t('prohibited_uses.items.0')}</li>
          <li>{t('prohibited_uses.items.1')}</li>
          <li>{t('prohibited_uses.items.2')}</li>
          <li>{t('prohibited_uses.items.3')}</li>
          <li>{t('prohibited_uses.items.4')}</li>
          <li>{t('prohibited_uses.items.5')}</li>
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('pricing_payments.title')}
        </h2>
        <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
          <li>{t('pricing_payments.items.0')}</li>
          <li>{t('pricing_payments.items.1')}</li>
          <li>{t('pricing_payments.items.2')}</li>
          <li>{t('pricing_payments.items.3')}</li>
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('termination.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('termination.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('disclaimer.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('disclaimer.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('limitation_liability.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('limitation_liability.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('governing_law.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('governing_law.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('changes_terms.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('changes_terms.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('contact.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
          {t('contact.content')}
        </p>
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <p className="text-gray-700 dark:text-gray-300">
            <strong>{t('contact.email')}:</strong> <EMAIL>
          </p>
        </div>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mt-4">
          {t('contact.acknowledgment')}
        </p>
      </section>
    </div>
  );
}
