import { respData, respErr } from "@/lib/resp";
import { getAllBills, getBillsTotal } from "@/models/bill";
import { isCurrentUserAdmin } from "@/services/admin";

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { page = 1, limit = 20 } = await req.json();

    // 获取账单列表
    const bills = await getAllBills(page, limit);
    const total = await getBillsTotal();

    return respData({
      bills: bills || [],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (e) {
    console.log("get admin bills failed: ", e);
    return respErr("获取账单列表失败");
  }
}
