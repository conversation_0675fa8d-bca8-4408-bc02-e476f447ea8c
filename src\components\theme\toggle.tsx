"use client";

import { BsMoonStars, BsSun } from "react-icons/bs";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

export default function () {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // 避免水合不匹配
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex items-center gap-x-2 px-2">
        <div className="cursor-pointer p-2 rounded-lg hover:bg-accent/50 transition-all duration-200">
          <div className="w-5 h-5" />
        </div>
      </div>
    );
  }

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  return (
    <div className="flex items-center gap-x-2 px-2">
      <button
        onClick={toggleTheme}
        className="cursor-pointer p-2 rounded-lg hover:bg-accent/50 transition-all duration-200 group focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        aria-label={theme === "dark" ? "Switch to light mode" : "Switch to dark mode"}
      >
        {theme === "dark" ? (
          <BsSun
            className="text-lg text-yellow-500 group-hover:text-yellow-400 transition-colors duration-200"
            width={20}
            height={20}
          />
        ) : (
          <BsMoonStars
            className="text-lg text-blue-600 group-hover:text-blue-500 transition-colors duration-200"
            width={20}
            height={20}
          />
        )}
      </button>
    </div>
  );
}
