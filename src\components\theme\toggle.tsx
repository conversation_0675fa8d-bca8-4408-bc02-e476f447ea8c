"use client";

import { BsMoonStars, BsSun } from "react-icons/bs";

import { Cache<PERSON>ey } from "@/services/constant";
import { cacheSet } from "@/lib/cache";
import { useAppContext } from "@/contexts/app";

export default function () {
  const { theme, setTheme } = useAppContext();

  const handleThemeChange = function (_theme: string) {
    if (_theme === theme) {
      return;
    }

    // 保存到缓存
    cacheSet(CacheKey.Theme, _theme, -1);

    // 更新应用状态
    setTheme(_theme);

    // 立即更新DOM类名
    if (typeof window !== 'undefined') {
      const root = window.document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(_theme);
    }
  };

  return (
    <div className="flex items-center gap-x-2 px-2">
      {theme === "dark" ? (
        <div
          className="cursor-pointer p-2 rounded-lg hover:bg-accent/50 transition-all duration-200 group"
          onClick={() => handleThemeChange("light")}
        >
          <BsSun
            className="text-lg text-yellow-500 group-hover:text-yellow-400 transition-colors duration-200"
            width={20}
            height={20}
          />
        </div>
      ) : (
        <div
          className="cursor-pointer p-2 rounded-lg hover:bg-accent/50 transition-all duration-200 group"
          onClick={() => handleThemeChange("dark")}
        >
          <BsMoonStars
            className="text-lg text-blue-600 group-hover:text-blue-500 transition-colors duration-200"
            width={20}
            height={20}
          />
        </div>
      )}
    </div>
  );
}
