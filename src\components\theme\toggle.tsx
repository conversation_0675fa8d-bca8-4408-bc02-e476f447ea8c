"use client";

import { BsMoonStars, BsSun } from "react-icons/bs";

import { <PERSON><PERSON><PERSON><PERSON> } from "@/services/constant";
import { cacheSet } from "@/lib/cache";
import { useAppContext } from "@/contexts/app";

export default function () {
  const { theme, setTheme } = useAppContext();

  const handleThemeChange = function (_theme: string) {
    if (_theme === theme) {
      return;
    }

    cacheSet(CacheKey.Theme, _theme, -1);
    setTheme(_theme);
  };

  return (
    <div className="flex items-center gap-x-2 px-2">
      {theme === "dark" ? (
        <div
          className="cursor-pointer p-2 rounded-lg hover:bg-accent/50 transition-all duration-200 group"
          onClick={() => handleThemeChange("light")}
        >
          <BsSun
            className="text-lg text-yellow-500 group-hover:text-yellow-400 transition-colors duration-200"
            width={20}
            height={20}
          />
        </div>
      ) : (
        <div
          className="cursor-pointer p-2 rounded-lg hover:bg-accent/50 transition-all duration-200 group"
          onClick={() => handleThemeChange("dark")}
        >
          <BsMoonStars
            className="text-lg text-blue-600 group-hover:text-blue-500 transition-colors duration-200"
            width={20}
            height={20}
          />
        </div>
      )}
    </div>
  );
}
