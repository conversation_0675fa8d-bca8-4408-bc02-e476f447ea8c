/**
 * 支付服务提供商枚举
 */
export enum PaymentProvider {
  STRIPE = 'stripe',
  PAYPAL = 'paypal'
}

/**
 * 支付会话创建参数
 */
export interface CreatePaymentSessionParams {
  order_no: string;
  product_id: string;
  product_name: string;
  amount: number;
  currency: string;
  interval: string;
  credits: number;
  user_email: string;
  user_uuid: string;
  valid_months: number;
  success_url: string;
  cancel_url: string;
  is_subscription: boolean;
}

/**
 * 支付会话响应
 */
export interface PaymentSessionResponse {
  session_id: string;
  public_key?: string;
  redirect_url?: string;
  order_no: string;
}

/**
 * 支付回调会话数据
 */
export interface PaymentCallbackSession {
  id: string;
  payment_status: string;
  customer_email?: string;
  customer_details?: {
    email?: string;
  };
  metadata?: {
    order_no?: string;
    user_email?: string;
    credits?: string;
    user_uuid?: string;
    product_name?: string;
  };
  amount_total?: number;
  currency?: string;
}

/**
 * 支付服务接口
 */
export interface IPaymentService {
  /**
   * 创建支付会话
   */
  createPaymentSession(params: CreatePaymentSessionParams): Promise<PaymentSessionResponse>;

  /**
   * 获取支付会话详情
   */
  getPaymentSession(session_id: string): Promise<PaymentCallbackSession>;

  /**
   * 验证webhook签名
   */
  verifyWebhookSignature(payload: string, signature: string, secret: string): any;

  /**
   * 获取支付服务提供商名称
   */
  getProviderName(): PaymentProvider;
}
