import { bills } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, gte, lte } from "drizzle-orm";

export enum BillTransactionType {
  Payment = "payment", // 支付
  Refund = "refund", // 退款
  Subscription = "subscription", // 订阅
}

export enum BillPurpose {
  CreditsPurchase = "credits_purchase", // 积分购买
  SubscriptionMonthly = "subscription_monthly", // 月度订阅
  SubscriptionYearly = "subscription_yearly", // 年度订阅
  OneTimePurchase = "one_time_purchase", // 一次性购买
}

export enum BillStatus {
  Completed = "completed", // 已完成
  Pending = "pending", // 待处理
  Failed = "failed", // 失败
  Refunded = "refunded", // 已退款
}

export async function insertBill(
  data: typeof bills.$inferInsert
): Promise<typeof bills.$inferSelect | undefined> {
  const [bill] = await db().insert(bills).values(data).returning();
  return bill;
}

export async function findBillByBillNo(
  bill_no: string
): Promise<typeof bills.$inferSelect | undefined> {
  const [bill] = await db()
    .select()
    .from(bills)
    .where(eq(bills.bill_no, bill_no))
    .limit(1);

  return bill;
}

export async function findBillByOrderNo(
  order_no: string
): Promise<typeof bills.$inferSelect | undefined> {
  const [bill] = await db()
    .select()
    .from(bills)
    .where(eq(bills.order_no, order_no))
    .limit(1);

  return bill;
}

export async function getBillsByUserUuid(
  user_uuid: string,
  page: number = 1,
  limit: number = 50
): Promise<(typeof bills.$inferSelect)[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(bills)
    .where(eq(bills.user_uuid, user_uuid))
    .orderBy(desc(bills.created_at))
    .limit(limit)
    .offset(offset);

  return data;
}

export async function getBillsByUserEmail(
  user_email: string,
  page: number = 1,
  limit: number = 50
): Promise<(typeof bills.$inferSelect)[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(bills)
    .where(eq(bills.user_email, user_email))
    .orderBy(desc(bills.created_at))
    .limit(limit)
    .offset(offset);

  return data;
}

export async function getBillsByDateRange(
  user_uuid: string,
  startDate: Date,
  endDate: Date
): Promise<(typeof bills.$inferSelect)[] | undefined> {
  const data = await db()
    .select()
    .from(bills)
    .where(
      and(
        eq(bills.user_uuid, user_uuid),
        gte(bills.created_at, startDate),
        lte(bills.created_at, endDate)
      )
    )
    .orderBy(desc(bills.created_at));

  return data;
}

export async function getAllBills(
  page: number = 1,
  limit: number = 50
): Promise<(typeof bills.$inferSelect)[] | undefined> {
  const offset = (page - 1) * limit;

  const data = await db()
    .select()
    .from(bills)
    .orderBy(desc(bills.created_at))
    .limit(limit)
    .offset(offset);

  return data;
}

export async function getBillsTotal(): Promise<number> {
  const total = await db().$count(bills);
  return total;
}

export async function updateBillStatus(
  bill_no: string,
  status: BillStatus
): Promise<typeof bills.$inferSelect | undefined> {
  const [bill] = await db()
    .update(bills)
    .set({ status })
    .where(eq(bills.bill_no, bill_no))
    .returning();

  return bill;
}
