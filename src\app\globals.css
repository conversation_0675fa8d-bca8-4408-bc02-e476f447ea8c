@import "tailwindcss";
@import "./theme.css";

@plugin "tailwindcss-animate";

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Dark mode gradient background - 定价页面风格 */
  .dark body {
    background: linear-gradient(to bottom right, #0f172a, #581c87, #0f172a);
    min-height: 100vh;
  }

  /* Light mode clean background */
  body:not(.dark) {
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
    min-height: 100vh;
  }
}

.container {
  @apply mx-auto max-w-7xl px-4 md:px-8;
}

input,
select,
textarea {
  @apply border-border outline-ring/50 bg-background;
}

button {
  @apply cursor-pointer border-border outline-ring/50;
}
