import "@/app/globals.css";
import { MdArrowBack } from "react-icons/md";
import { Metadata } from "next";
import React from "react";
import { getTranslations } from "next-intl/server";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations();

  return {
    title: {
      template: `%s | ${t("metadata.title")}`,
      default: t("metadata.title"),
    },
    description: t("metadata.description"),
    keywords: t("metadata.keywords"),
  };
}

export default async function LegalLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal' });

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-purple-900 dark:to-slate-900">
      <div className="container mx-auto max-w-4xl">
        {/* 改进的返回按钮 */}
        <div className="pt-8 pb-4 px-8">
          <a
            className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200 font-medium"
            href="/"
          >
            <MdArrowBack className="text-xl" />
            <span>{t('back_to_home')}</span>
          </a>
        </div>

        {/* 内容区域 */}
        <div className="bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm rounded-lg shadow-xl mx-8 mb-8">
          <div className="text-md leading-loose pt-8 pb-8 px-8 max-w-none">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
