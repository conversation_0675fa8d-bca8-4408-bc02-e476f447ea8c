{"id": "71ca1a11-40c7-4664-b319-715430fa3f92", "prevId": "018f67ae-0113-4346-b5c5-48e925457217", "version": "7", "dialect": "postgresql", "tables": {"public.affiliates": {"name": "affiliates", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "affiliates_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "''"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "paid_order_no": {"name": "paid_order_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "''"}, "paid_amount": {"name": "paid_amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "reward_percent": {"name": "reward_percent", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "reward_amount": {"name": "reward_amount", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.apikeys": {"name": "apikeys", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "apikeys_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "api_key": {"name": "api_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"apikeys_api_key_unique": {"name": "apikeys_api_key_unique", "nullsNotDistinct": false, "columns": ["api_key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credits": {"name": "credits", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "credits_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "trans_no": {"name": "trans_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "trans_type": {"name": "trans_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true}, "order_no": {"name": "order_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "expired_at": {"name": "expired_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"credits_trans_no_unique": {"name": "credits_trans_no_unique", "nullsNotDistinct": false, "columns": ["trans_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedbacks": {"name": "feedbacks", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "feedbacks_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.orders": {"name": "orders", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "orders_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "order_no": {"name": "order_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "user_uuid": {"name": "user_uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "''"}, "user_email": {"name": "user_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "''"}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "interval": {"name": "interval", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "expired_at": {"name": "expired_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "stripe_session_id": {"name": "stripe_session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "payment_provider": {"name": "payment_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'stripe'"}, "payment_session_id": {"name": "payment_session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "sub_id": {"name": "sub_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sub_interval_count": {"name": "sub_interval_count", "type": "integer", "primaryKey": false, "notNull": false}, "sub_cycle_anchor": {"name": "sub_cycle_anchor", "type": "integer", "primaryKey": false, "notNull": false}, "sub_period_end": {"name": "sub_period_end", "type": "integer", "primaryKey": false, "notNull": false}, "sub_period_start": {"name": "sub_period_start", "type": "integer", "primaryKey": false, "notNull": false}, "sub_times": {"name": "sub_times", "type": "integer", "primaryKey": false, "notNull": false}, "product_id": {"name": "product_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "product_name": {"name": "product_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "valid_months": {"name": "valid_months", "type": "integer", "primaryKey": false, "notNull": false}, "order_detail": {"name": "order_detail", "type": "text", "primaryKey": false, "notNull": false}, "paid_at": {"name": "paid_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "paid_email": {"name": "paid_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "paid_detail": {"name": "paid_detail", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"orders_order_no_unique": {"name": "orders_order_no_unique", "nullsNotDistinct": false, "columns": ["order_no"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "posts_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "cover_url": {"name": "cover_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "author_name": {"name": "author_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "author_avatar_url": {"name": "author_avatar_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"posts_uuid_unique": {"name": "posts_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "identity": {"type": "always", "name": "users_id_seq", "schema": "public", "increment": "1", "startWith": "1", "minValue": "1", "maxValue": "2147483647", "cache": "1", "cycle": false}}, "uuid": {"name": "uuid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "nickname": {"name": "nickname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "signin_type": {"name": "signin_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "signin_ip": {"name": "signin_ip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "signin_provider": {"name": "signin_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "signin_openid": {"name": "signin_openid", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "invite_code": {"name": "invite_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "''"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "''"}, "is_affiliate": {"name": "is_affiliate", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"email_provider_unique_idx": {"name": "email_provider_unique_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "signin_provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_uuid_unique": {"name": "users_uuid_unique", "nullsNotDistinct": false, "columns": ["uuid"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}