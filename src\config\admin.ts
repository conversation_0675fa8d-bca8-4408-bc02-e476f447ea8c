// 管理员配置
export const ADMIN_CONFIG = {
  // 管理员邮箱列表 - 在这里添加您的Google账户邮箱
  adminEmails: [
    '<EMAIL>',  // 您的管理员邮箱
    // 示例：'<EMAIL>',
    // 可以添加更多管理员邮箱
  ],
};

// 检查邮箱是否为管理员
export function isAdminEmail(email: string): boolean {
  return ADMIN_CONFIG.adminEmails.includes(email.toLowerCase());
}

// 从环境变量获取管理员邮箱（可选）
export function getAdminEmailsFromEnv(): string[] {
  const envAdmins = process.env.ADMIN_EMAILS;
  if (envAdmins) {
    return envAdmins.split(',').map(email => email.trim().toLowerCase());
  }
  return [];
}

// 获取所有管理员邮箱（配置文件 + 环境变量）
export function getAllAdminEmails(): string[] {
  const configEmails = ADMIN_CONFIG.adminEmails.map(email => email.toLowerCase());
  const envEmails = getAdminEmailsFromEnv();
  return [...new Set([...configEmails, ...envEmails])];
}
