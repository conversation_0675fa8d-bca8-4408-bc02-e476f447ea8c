import { respData, respErr } from "@/lib/resp";
import { getOrders, getOrdersTotal } from "@/models/order";
import { isCurrentUserAdmin } from "@/services/admin";

export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    const { page = 1, limit = 20 } = await req.json();

    // 获取订单列表
    const orders = await getOrders(page, limit);
    const total = await getOrdersTotal();

    return respData({
      orders: orders || [],
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    });
  } catch (e) {
    console.log("get admin orders failed: ", e);
    return respErr("获取订单列表失败");
  }
}
